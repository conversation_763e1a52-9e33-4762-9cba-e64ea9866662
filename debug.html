<!DOCTYPE html>
<html>
<head>
    <title>User Store Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; }
        .output { background: #f5f5f5; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>User Store Debug</h1>
    
    <div class="section">
        <h2>LocalStorage Contents</h2>
        <button onclick="showLocalStorage()">Show LocalStorage</button>
        <button onclick="clearLocalStorage()">Clear LocalStorage</button>
        <div id="localStorage-output" class="output"></div>
    </div>
    
    <div class="section">
        <h2>User Profile Data</h2>
        <button onclick="showUserProfile()">Show User Profile</button>
        <button onclick="simulateProfileComplete()">Simulate Profile Complete</button>
        <button onclick="simulateProfileIncomplete()">Simulate Profile Incomplete</button>
        <div id="profile-output" class="output"></div>
    </div>
    
    <div class="section">
        <h2>IndexedDB Debug</h2>
        <button onclick="showIndexedDB()">Show IndexedDB</button>
        <button onclick="clearIndexedDB()">Clear IndexedDB</button>
        <div id="indexeddb-output" class="output"></div>
    </div>
    
    <script>
        function showLocalStorage() {
            const output = document.getElementById('localStorage-output');
            const data = {};
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                data[key] = value;
            }
            
            output.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }
        
        function clearLocalStorage() {
            localStorage.clear();
            showLocalStorage();
        }
        
        function showUserProfile() {
            const output = document.getElementById('profile-output');
            const userStorage = localStorage.getItem('user-profile-storage');
            
            if (userStorage) {
                try {
                    const parsed = JSON.parse(userStorage);
                    output.innerHTML = '<pre>' + JSON.stringify(parsed, null, 2) + '</pre>';
                } catch (e) {
                    output.innerHTML = 'Error parsing user profile: ' + e.message;
                }
            } else {
                output.innerHTML = 'No user profile found in localStorage';
            }
        }
        
        function simulateProfileComplete() {
            const profileData = {
                state: {
                    profile: {
                        id: 'test-user',
                        name: 'Test User',
                        weight: 70,
                        height: 170,
                        age: 25,
                        gender: 'male',
                        targetWeight: 65,
                        targetDays: 30,
                        activityLevel: 'moderate',
                        bmr: 1800,
                        tdee: 2200,
                        dailyCalorieLimit: 1800,
                        mealRatios: {
                            breakfast: 0.3,
                            lunch: 0.4,
                            dinner: 0.3
                        },
                        weightHistory: [],
                        metabolicInfo: {
                            bmr: 1800,
                            tdee: 2200,
                            lastUpdated: new Date().toISOString()
                        },
                        preferences: {
                            theme: 'system',
                            language: 'zh-CN',
                            notifications: {
                                mealReminders: true,
                                dailySummary: true,
                                weeklyReport: false
                            },
                            units: {
                                weight: 'kg',
                                height: 'cm'
                            }
                        },
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    isProfileComplete: true
                },
                version: 1
            };
            
            localStorage.setItem('user-profile-storage', JSON.stringify(profileData));
            showUserProfile();
        }
        
        function simulateProfileIncomplete() {
            const profileData = {
                state: {
                    profile: null,
                    isProfileComplete: false
                },
                version: 1
            };
            
            localStorage.setItem('user-profile-storage', JSON.stringify(profileData));
            showUserProfile();
        }
        
        function showIndexedDB() {
            const output = document.getElementById('indexeddb-output');
            
            const request = indexedDB.open('KcalTrackerDB', 1);
            
            request.onsuccess = function(event) {
                const db = event.target.result;
                const stores = Array.from(db.objectStoreNames);
                output.innerHTML = 'IndexedDB stores: ' + stores.join(', ');
                db.close();
            };
            
            request.onerror = function(event) {
                output.innerHTML = 'Error opening IndexedDB: ' + event.target.error;
            };
        }
        
        function clearIndexedDB() {
            const request = indexedDB.deleteDatabase('KcalTrackerDB');
            
            request.onsuccess = function() {
                document.getElementById('indexeddb-output').innerHTML = 'IndexedDB cleared successfully';
            };
            
            request.onerror = function(event) {
                document.getElementById('indexeddb-output').innerHTML = 'Error clearing IndexedDB: ' + event.target.error;
            };
        }
        
        // 初始化显示
        window.onload = function() {
            showLocalStorage();
            showUserProfile();
            showIndexedDB();
        };
    </script>
</body>
</html>