#!/bin/bash

# 测试用户状态恢复的脚本

echo "=== 用户状态恢复测试 ==="

# 1. 检查关键文件是否存在
echo "1. 检查关键文件..."
FILES=(
    "src/domains/user/stores/userStore.ts"
    "src/domains/user/hooks/useUserInitialization.ts"
    "src/app/router/index.tsx"
    "src/shared/components/atoms/LoadingScreen.tsx"
    "src/App.tsx"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 不存在"
        exit 1
    fi
done

# 2. 检查关键代码片段
echo ""
echo "2. 检查关键代码片段..."

# 检查 userStore 中的 hydrated 状态
if grep -q "hydrated: boolean" src/domains/user/stores/userStore.ts; then
    echo "✓ userStore 包含 hydrated 状态"
else
    echo "✗ userStore 缺少 hydrated 状态"
    exit 1
fi

# 检查 onRehydrateStorage 回调
if grep -q "onRehydrateStorage" src/domains/user/stores/userStore.ts; then
    echo "✓ userStore 包含 onRehydrateStorage 回调"
else
    echo "✗ userStore 缺少 onRehydrateStorage 回调"
    exit 1
fi

# 检查路由守卫中的 hydrated 检查
if grep -q "if (!hydrated)" src/app/router/index.tsx; then
    echo "✓ 路由守卫包含 hydrated 检查"
else
    echo "✗ 路由守卫缺少 hydrated 检查"
    exit 1
fi

# 检查 LoadingScreen 的使用
if grep -q "LoadingScreen" src/app/router/index.tsx; then
    echo "✓ 路由守卫使用 LoadingScreen"
else
    echo "✗ 路由守卫缺少 LoadingScreen"
    exit 1
fi

# 检查 App.tsx 中的初始化钩子
if grep -q "useUserInitialization" src/App.tsx; then
    echo "✓ App.tsx 使用 useUserInitialization"
else
    echo "✗ App.tsx 缺少 useUserInitialization"
    exit 1
fi

echo ""
echo "3. 编译检查..."
# 检查 TypeScript 编译
if npm run type-check 2>/dev/null; then
    echo "✓ TypeScript 编译通过"
else
    echo "! TypeScript 编译可能有问题，请检查"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "修复摘要:"
echo "1. ✓ 添加了 hydrated 状态来跟踪 Zustand 状态恢复"
echo "2. ✓ 添加了 onRehydrateStorage 回调来标记状态恢复完成"
echo "3. ✓ 更新了所有路由守卫以等待状态恢复"
echo "4. ✓ 添加了 LoadingScreen 组件提供更好的用户体验"
echo "5. ✓ 添加了 useUserInitialization 钩子进行状态初始化"
echo "6. ✓ 更新了 App.tsx 以使用初始化钩子"
echo ""
echo "这些修复应该解决页面刷新后总是跳转到 /setup 的问题。"