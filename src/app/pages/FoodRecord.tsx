import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useNutritionStore } from '@/domains/nutrition/stores/nutritionStore';
import { useUserStore } from '@/domains/user/stores/userStore';
import { FoodRecord as FoodRecordType, MealType } from '@/shared/types';
import BottomNavigation from '@/shared/components/navigation/BottomNavigationNew';
import FoodNutritionModal from '@/shared/components/modals/FoodNutritionModal';
import { formatDate } from '@/shared/utils';
import { WeightDisplay, CalorieDisplay } from '@/shared/components/atoms';
import { getCurrentSnackTimeInfo, SNACK_PERIODS } from '@/shared/utils/snackTimeUtils';
import { useCountdown, formatCountdownText, getCountdownStyle } from '@/shared/hooks/useCountdown';
// import anime from 'animejs/lib/anime.es.js';

// 餐次配置
const MEAL_CONFIG = {
  breakfast: {
    label: '早餐',
    icon: '🌅',
    timeRange: '0:00-10:00',
    color: 'text-orange-600 bg-orange-50 border-orange-200'
  },
  lunch: { 
    label: '午餐', 
    icon: '☀️', 
    timeRange: '11:00-14:00',
    color: 'text-green-600 bg-green-50 border-green-200'
  },
  dinner: { 
    label: '晚餐', 
    icon: '🌙', 
    timeRange: '17:00-21:00',
    color: 'text-blue-600 bg-blue-50 border-blue-200'
  },
  snack: {
    label: '加餐',
    icon: '🍎',
    timeRange: '随时',
    color: 'text-emerald-600 bg-emerald-50 border-emerald-200'
  }
};

// 获取当前餐次
const getCurrentMealType = (): MealType => {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 11) return 'breakfast';
  if (hour >= 11 && hour < 15) return 'lunch';
  if (hour >= 17 && hour < 22) return 'dinner';
  return 'snack';
};

// 获取动态的加餐配置
const getSnackConfig = () => {
  const snackInfo = getCurrentSnackTimeInfo();
  const currentPeriod = snackInfo.currentPeriod || snackInfo.nextPeriod;

  return {
    label: currentPeriod?.name || '加餐',
    icon: currentPeriod?.emoji || '🍎',
    timeRange: currentPeriod?.timeRange || '随时',
    color: 'text-emerald-600 bg-emerald-50 border-emerald-200',
    description: snackInfo.isSnackTime
      ? `现在是${currentPeriod?.name}时间`
      : `${currentPeriod?.name}将在${snackInfo.timeUntilNext}开始`
  };
};

// 获取特定加餐时间段的配置
const getSpecificSnackConfig = (snackType: string) => {
  const snackPeriod = SNACK_PERIODS.find(period => period.id === snackType.replace('-snack', ''));
  const snackInfo = getCurrentSnackTimeInfo();
  const isCurrentPeriod = snackInfo.currentPeriod?.id === snackPeriod?.id;

  if (!snackPeriod) {
    return {
      label: '加餐',
      icon: '🍎',
      timeRange: '随时',
      color: 'text-emerald-600 bg-emerald-50 border-emerald-200',
      description: ''
    };
  }

  return {
    label: snackPeriod.name,
    icon: snackPeriod.emoji,
    timeRange: snackPeriod.timeRange,
    color: 'text-emerald-600 bg-emerald-50 border-emerald-200',
    description: isCurrentPeriod ? `现在是${snackPeriod.name}时间` : snackPeriod.description
  };
};

const FoodRecordPage: React.FC = () => {
  const navigate = useNavigate();
  const { profile } = useUserStore();
  const {
    getDailyFoodRecords,
    addDetailedFoodRecord,
    updateFoodRecord,
    deleteFoodRecord
  } = useNutritionStore();

  // 状态管理
  const [currentDate, setCurrentDate] = useState(new Date());
  const [isAIProcessing, setIsAIProcessing] = useState(false); // AI处理状态

  // 营养详情弹窗状态
  const [showNutritionModal, setShowNutritionModal] = useState(false);
  const [selectedFoodRecord, setSelectedFoodRecord] = useState<FoodRecordType | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // 使用实时倒计时Hook
  const countdownInfo = useCountdown();

  // 移除编辑状态，改为使用营养详情弹窗的编辑模式

  // 获取当日食物记录
  const dailyRecords = getDailyFoodRecords(currentDate);

  // // 临时添加测试数据用于桌面端适配测试
  // React.useEffect(() => {
  //   if (!dailyRecords || dailyRecords.records.length === 0) {
  //     // 添加一些测试食物记录
  //     const testFoods = [
  //       {
  //         name: '白米饭',
  //         weight: 150,
  //         calories: 195,
  //         mealType: 'breakfast' as MealType,
  //         recordedAt: currentDate,
  //         nutrition: { protein: 4, fat: 0.5, carbs: 43, fiber: 0.4, sugar: 0.1, sodium: 1 },
  //         aiRecognition: { confidence: 0.9, method: 'text' as const, originalInput: '白米饭', dataSource: 'text_analysis' as const },
  //         isEdited: false
  //       },
  //       {
  //         name: '鸡胸肉',
  //         weight: 100,
  //         calories: 165,
  //         mealType: 'breakfast' as MealType,
  //         recordedAt: currentDate,
  //         nutrition: { protein: 31, fat: 3.6, carbs: 0, fiber: 0, sugar: 0, sodium: 74 },
  //         aiRecognition: { confidence: 0.95, method: 'image' as const, originalInput: '鸡胸肉', dataSource: 'visual_estimation' as const },
  //         isEdited: false
  //       },
  //       {
  //         name: '西兰花',
  //         weight: 80,
  //         calories: 27,
  //         mealType: 'breakfast' as MealType,
  //         recordedAt: currentDate,
  //         nutrition: { protein: 3, fat: 0.3, carbs: 5, fiber: 2.6, sugar: 1.5, sodium: 33 },
  //         aiRecognition: { confidence: 0.88, method: 'text' as const, originalInput: '西兰花', dataSource: 'text_analysis' as const },
  //         isEdited: false
  //       },
  //       {
  //         name: '苹果',
  //         weight: 120,
  //         calories: 62,
  //         mealType: 'breakfast' as MealType,
  //         recordedAt: currentDate,
  //         nutrition: { protein: 0.3, fat: 0.2, carbs: 16, fiber: 2.4, sugar: 12, sodium: 1 },
  //         aiRecognition: { confidence: 0.92, method: 'image' as const, originalInput: '苹果', dataSource: 'visual_estimation' as const },
  //         isEdited: false
  //       },
  //       {
  //         name: '牛奶',
  //         weight: 200,
  //         calories: 122,
  //         mealType: 'breakfast' as MealType,
  //         recordedAt: currentDate,
  //         nutrition: { protein: 6.6, fat: 6.8, carbs: 9.6, fiber: 0, sugar: 9.6, sodium: 88 },
  //         aiRecognition: { confidence: 0.85, method: 'text' as const, originalInput: '牛奶', dataSource: 'nutrition_facts' as const },
  //         isEdited: false
  //       }
  //     ];

  //     testFoods.forEach(food => {
  //       addDetailedFoodRecord(currentDate, food);
  //     });
  //   }
  // }, [currentDate, dailyRecords, addDetailedFoodRecord]);

  // 页面动画初始化 (暂时禁用)
  useEffect(() => {
    // TODO: 修复anime.js导入问题后重新启用动画
    // if (activeView === 'list') {
    //   const timelineItems = document.querySelectorAll('.food-timeline-item');
    //   if (timelineItems.length > 0) {
    //     anime({
    //       targets: '.food-timeline-item',
    //       translateY: [20, 0],
    //       opacity: [0, 1],
    //       delay: anime.stagger(100),
    //       duration: 600,
    //       easing: 'easeOutQuad'
    //     });
    //   }
    // }
  }, [dailyRecords]);



  // 删除食物记录
  const handleDeleteRecord = (recordId: string) => {
    if (window.confirm('确定要删除这条食物记录吗？')) {
      // 直接删除数据 (动画暂时禁用)
      deleteFoodRecord(currentDate, recordId);

      // TODO: 修复anime.js导入问题后重新启用删除动画
      // const targetElement = document.querySelector(`[data-record-id="${recordId}"]`);
      // if (targetElement) {
      //   anime({
      //     targets: `[data-record-id="${recordId}"]`,
      //     translateX: [-300],
      //     opacity: [0],
      //     duration: 400,
      //     easing: 'easeInQuad',
      //     complete: () => {
      //       deleteFoodRecord(currentDate, recordId);
      //     }
      //   });
      // }
    }
  };

  // 编辑食物记录 - 改为打开营养详情弹窗并进入编辑状态
  const handleEditRecord = (record: FoodRecordType) => {
    setSelectedFoodRecord(record);
    setIsEditMode(true);
    setShowNutritionModal(true);
  };

  // 移除handleSaveEdit函数，编辑保存现在通过营养详情弹窗处理





  // 渲染时间轴视图
  const renderTimelineView = () => {
    const mealTypes: (MealType | string)[] = ['breakfast', 'lunch', 'dinner', 'morning-snack', 'afternoon-snack', 'evening-snack'];
    
    return (
      <div className="space-y-6">
        {/* 日期选择器 - DaisyUI风格 */}
        <div className="dropdown dropdown-bottom dropdown-center w-full mx-auto">
          <div className="flex items-center justify-center bg-white rounded-xl p-3 xl:p-4 shadow-sm border border-slate-200 mb-1 hover:shadow-md transition-shadow">
            <div className="flex items-center gap-3">
              <button
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡，防止触发dropdown
                  setCurrentDate(new Date(currentDate.getTime() - 24 * 60 * 60 * 1000));
                }}
                className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0 hover:bg-slate-100 transition-colors"
                tabIndex={-1}
              >
                ←
              </button>
              {/* {{ AURA-X: Fix - 阻止日期切换按钮触发下拉菜单. Approval: 寸止(ID:1736938600). }} */}
              <div
                tabIndex={0}
                role="button"
                className="text-center min-w-[100px] cursor-pointer hover:bg-slate-50 rounded-lg p-2 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    // 可以在这里添加日期选择器逻辑
                  }
                }}
              >
                <div className="font-bold text-slate-800 text-base xl:text-lg">
                  {formatDate(currentDate, 'MM月dd日')}
                </div>
                <div className="text-xs text-slate-500 -mt-1">
                  {formatDate(currentDate, 'EEEE')}
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation(); // 阻止事件冒泡，防止触发dropdown
                  setCurrentDate(new Date(currentDate.getTime() + 24 * 60 * 60 * 1000));
                }}
                className="btn btn-ghost btn-sm w-8 h-8 min-h-8 p-0 hover:bg-slate-100 transition-colors"
                disabled={formatDate(currentDate, 'yyyy-MM-dd') >= formatDate(new Date(), 'yyyy-MM-dd')}
                tabIndex={-1}
              >
                →
              </button>
              {/* {{ AURA-X: Fix - 阻止日期切换按钮触发下拉菜单. Approval: 寸止(ID:1736938600). }} */}
            </div>
          </div>

          {/* DaisyUI下拉菜单 */}
          <div tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-4 shadow-lg border border-base-300">
            <div className="text-center mb-4">
              <h3 className="font-semibold text-base-content">选择日期</h3>
            </div>

            {/* 快速日期选择 */}
            <div className="grid grid-cols-3 gap-2 mb-4">
              <button
                onClick={() => {
                  setCurrentDate(new Date());
                  // 关闭dropdown
                  const dropdown = document.activeElement as HTMLElement;
                  dropdown?.blur();
                }}
                className="btn btn-outline btn-sm"
              >
                今天
              </button>
              <button
                onClick={() => {
                  const yesterday = new Date();
                  yesterday.setDate(yesterday.getDate() - 1);
                  setCurrentDate(yesterday);
                  // 关闭dropdown
                  const dropdown = document.activeElement as HTMLElement;
                  dropdown?.blur();
                }}
                className="btn btn-outline btn-sm"
              >
                昨天
              </button>
              <button
                onClick={() => {
                  const threeDaysAgo = new Date();
                  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
                  setCurrentDate(threeDaysAgo);
                  // 关闭dropdown
                  const dropdown = document.activeElement as HTMLElement;
                  dropdown?.blur();
                }}
                className="btn btn-outline btn-sm"
              >
                3天前
              </button>
            </div>

            {/* 日期输入 */}
            <div className="form-control mb-4">
              <label className="label">
                <span className="label-text">选择具体日期</span>
              </label>
              <input
                type="date"
                value={formatDate(currentDate, 'yyyy-MM-dd')}
                onChange={(e) => {
                  if (e.target.value) {
                    setCurrentDate(new Date(e.target.value));
                    // 关闭dropdown
                    const dropdown = document.activeElement as HTMLElement;
                    dropdown?.blur();
                  }
                }}
                max={formatDate(new Date(), 'yyyy-MM-dd')}
                className="input input-bordered w-full xl:input-lg bg-white"
              />
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => {
                  // 关闭dropdown
                  const dropdown = document.activeElement as HTMLElement;
                  dropdown?.blur();
                }}
                className="btn btn-ghost btn-sm"
              >
                关闭
              </button>
            </div>
          </div>
        </div>

        {/* 左对齐时间轴 */}
        <div className="space-y-4">
          {mealTypes.map((mealType, index) => {
            // 处理加餐时间段的记录获取
            let mealRecords: any[] = [];
            let mealConfig: any;

            if (typeof mealType === 'string' && mealType.includes('-snack')) {
              // 加餐时间段：从snack类型的记录中筛选
              const allSnackRecords = dailyRecords?.mealRecords['snack'] || [];
              const snackPeriod = SNACK_PERIODS.find(period => period.id === mealType.replace('-snack', ''));

              // 根据时间段筛选记录（这里简化处理，实际可以根据记录时间筛选）
              mealRecords = allSnackRecords.filter((record: any) => {
                if (!snackPeriod) return false;
                const recordTime = new Date(record.recordedAt);
                const recordHour = recordTime.getHours();
                return recordHour >= snackPeriod.startHour && recordHour < snackPeriod.endHour;
              });

              mealConfig = getSpecificSnackConfig(mealType);
            } else {
              // 主餐
              mealRecords = dailyRecords?.mealRecords[mealType as MealType] || [];
              mealConfig = MEAL_CONFIG[mealType as MealType];
            }

            const totalCalories = mealRecords.reduce((sum: number, record: any) => sum + record.calories, 0);

            return (
              <div key={mealType} className={`food-timeline-item ${index === 0 ? 'mt-4' : ''}`}>
                {/* 餐次标题行 */}
                <div className="flex items-center gap-3 xl:gap-4 mb-3 xl:mb-4">
                  <div className={`w-10 h-10 xl:w-12 xl:h-12 rounded-full flex items-center justify-center ${mealConfig.color} border-2 flex-shrink-0`}>
                    <span className="text-lg xl:text-xl">{mealConfig.icon}</span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <h3 className="font-bold text-slate-800 text-lg xl:text-xl">{mealConfig.label}</h3>
                        <div className={`badge ${mealConfig.color} font-medium text-xs xl:text-sm`}>
                          {mealConfig.timeRange}
                        </div>
                      </div>
                      <div className="text-sm xl:text-base text-slate-500 font-medium">
                        {totalCalories} kcal
                      </div>
                    </div>
                    {mealType === 'snack' && (
                      <div className={`text-xs mt-1 p-2 rounded-lg ${getCountdownStyle(countdownInfo.isSnackTime).bgColor} ${getCountdownStyle(countdownInfo.isSnackTime).borderColor} border`}>
                        <span className={getCountdownStyle(countdownInfo.isSnackTime).textColor}>
                          {getCountdownStyle(countdownInfo.isSnackTime).icon} {formatCountdownText(countdownInfo.snackInfo)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 食物记录卡片区域 */}
                <div className="mb-6">
                  {mealRecords.length === 0 ? (
                    <div className="bg-white rounded-xl p-6 xl:p-8 text-center text-slate-400 shadow-sm border border-slate-200 hover:shadow-md transition-shadow">
                      <div className="text-3xl xl:text-4xl mb-2">🍽️</div>
                      <div className="text-sm xl:text-base">暂无记录</div>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-3 xl:gap-4">
                      {mealRecords.map((record) => (
                        <div
                          key={record.id}
                          data-record-id={record.id}
                          className="bg-white rounded-lg border border-slate-200 p-4 xl:p-5 shadow-sm hover:shadow-lg hover:scale-[1.02] transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 min-w-[280px] xl:min-w-[320px]"
                          onClick={() => handleViewNutrition(record)}
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              handleViewNutrition(record);
                            }
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0"> {/* {{ AURA-X: Modify - 添加min-w-0确保flex子元素能正确收缩. Approval: 寸止(ID:1736937100). }} */}
                              <div className="flex items-center gap-2">
                                <div className="font-medium text-slate-800 text-base xl:text-lg truncate flex-1" title={record.name}> {/* {{ AURA-X: Modify - 移除固定宽度，使用flex-1自适应. Approval: 寸止(ID:1736937100). }} */}
                                  {record.name}
                                </div>
                                {/* 数据源标识 */}
                                {record.aiRecognition && (
                                  <>
                                    {record.aiRecognition.dataSource === 'text_analysis' && (
                                      <span className="text-xs xl:text-sm bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">
                                        📝 文本识别
                                      </span>
                                    )}
                                    {record.aiRecognition.dataSource === 'visual_estimation' && (
                                      <span className="text-xs xl:text-sm bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                        👁️ 视觉识别
                                      </span>
                                    )}
                                    {record.aiRecognition.dataSource === 'nutrition_label' && (
                                      <span className="text-xs xl:text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                        📝 营养标签
                                      </span>
                                    )}
                                    {/* 向后兼容：如果没有dataSource，使用method */}
                                    {!record.aiRecognition.dataSource && record.aiRecognition.method === 'text' && (
                                      <span className="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">
                                        📝 文本识别
                                      </span>
                                    )}
                                    {!record.aiRecognition.dataSource && record.aiRecognition.method === 'image' && (
                                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                        👁️ 视觉识别
                                      </span>
                                    )}
                                  </>
                                )}
                              </div>
                              <div className="text-sm xl:text-base text-slate-500 mt-1">
                                <WeightDisplay value={record.weight} /> • <CalorieDisplay value={record.calories} />
                                {record.isEdited && (
                                  <span className="ml-2 badge badge-xs xl:badge-sm badge-warning">
                                    已编辑
                                  </span>
                                )}
                              </div>
                              <div className="text-xs xl:text-sm text-slate-400 mt-1">
                                添加于 {new Date(record.recordedAt).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                                {record.isEdited && (
                                  <span className="ml-2">
                                    • 已编辑
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-1 xl:gap-2 ml-2">
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditRecord(record);
                                }}
                                className="btn btn-ghost btn-sm xl:btn-md w-8 h-8 xl:w-10 xl:h-10 min-h-8 xl:min-h-10 p-0 hover:bg-blue-50 hover:scale-105 transition-all duration-200"
                                title="编辑"
                                tabIndex={0}
                              >
                                ✏️
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteRecord(record.id);
                                }}
                                className="btn btn-ghost btn-sm xl:btn-md w-8 h-8 xl:w-10 xl:h-10 min-h-8 xl:min-h-10 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 hover:scale-105 transition-all duration-200"
                                title="删除"
                                tabIndex={0}
                              >
                                🗑️
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  if (!profile) {
    return (
      <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="text-6xl mb-4">👤</div>
          <h2 className="text-xl font-bold mb-2">未找到用户档案</h2>
          <p className="text-slate-600 mb-4">请先完成个人档案设置</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/setup')}
          >
            立即设置
          </button>
        </div>
      </div>
    );
  }

  // 处理营养详情查看
  const handleViewNutrition = (record: FoodRecordType) => {
    setSelectedFoodRecord(record);
    setIsEditMode(false);
    setShowNutritionModal(true);
  };

  // 处理营养详情保存
  const handleSaveNutrition = (updatedRecord: FoodRecordType, newDate?: Date) => {
    if (newDate && newDate.toDateString() !== currentDate.toDateString()) {
      // {{ AURA-X: Modify - 修复日期变更时的数据同步问题. Approval: 寸止(ID:1736937100). }}
      // 如果日期有变化，需要先从原日期删除，再添加到新日期
      deleteFoodRecord(currentDate, updatedRecord.id);

      // 添加到新日期
      const newRecord = {
        ...updatedRecord,
        recordedAt: newDate,
        isEdited: true
      };
      addDetailedFoodRecord(newDate, newRecord);

      // 同步当前显示的日期到新日期，确保用户能看到保存后的结果
      setCurrentDate(newDate);
    } else {
      // 日期没有变化，直接更新
      updateFoodRecord(currentDate, updatedRecord.id, {
        name: updatedRecord.name,
        weight: updatedRecord.weight,
        calories: updatedRecord.calories,
        nutrition: updatedRecord.nutrition,
        isEdited: true
      });
    }
    setShowNutritionModal(false);
    setSelectedFoodRecord(null);
  };

  // 处理识别完成
  const handleRecognitionComplete = (result: any) => {
    console.log('识别完成:', result);

    if (result && result.foods && result.foods.length > 0) {
      // 使用选择的日期或当前日期
      const targetDate = result.selectedDate || currentDate;

      // 为每个识别的食物创建记录
      result.foods.forEach((food: any) => {
        // {{ AURA-X: Modify - 增强数据验证，防止undefined和无效数据错误. Approval: 寸止(ID:1736937400). }}
        if (!food || typeof food !== 'object') {
          console.warn('无效的食物数据，跳过:', food);
          return;
        }

        const calories = Number(food.calories) || 0;
        const weight = Number(food.weight) || 100;

        // 验证必要数据
        if (calories <= 0 || isNaN(calories)) {
          console.warn('无效的卡路里数据，跳过食物记录:', { food, calories });
          return;
        }

        const foodRecord = {
          name: food.name || '未知食物',
          weight: weight, // 使用AI识别的重量，默认100g
          calories: calories,
          mealType: result.meal,
          recordedAt: targetDate,
          nutrition: food.nutrition || {
            protein: Math.round(calories * 0.15 / 4), // 备用估算
            fat: Math.round(calories * 0.25 / 9),
            carbs: Math.round(calories * 0.6 / 4),
            fiber: Math.round(calories * 0.05 / 4),
            sugar: Math.round(calories * 0.1 / 4),
            sodium: Math.round(calories * 0.5) // 钠含量估算(mg)
          },
          aiRecognition: {
            confidence: Number(food.confidence) || 0.85, // 使用AI返回的置信度
            method: result.method,
            originalInput: result.content || '',
            dataSource: food.dataSource === 'nutrition_facts' ? 'nutrition_label' : (food.dataSource || (result.method === 'text' ? 'text_analysis' : 'visual_estimation'))
          },
          isEdited: false
        };

        // 添加到营养存储（使用目标日期）
        addDetailedFoodRecord(targetDate, foodRecord);
      });

      console.log('食物记录已保存到营养存储');
    }
  };

  return (
    <div className="min-h-screen bg-base-200 pb-16">
      {/* 主要内容 - 只显示时间线视图 */}
      <div className="max-w-lg xl:max-w-5xl 2xl:max-w-7xl mx-auto px-4 xl:px-6 pt-4 pb-4">
        {renderTimelineView()}
      </div>



      <BottomNavigation
        showAddButton={true}
        onRecognitionComplete={handleRecognitionComplete}
        currentDate={currentDate}
        isAIProcessing={isAIProcessing}
        onProcessingStateChange={setIsAIProcessing}
      />

      {/* 编辑功能已整合到营养详情弹窗中 */}

      {/* 营养详情弹窗 */}
      <FoodNutritionModal
        isOpen={showNutritionModal}
        onClose={() => {
          setShowNutritionModal(false);
          setSelectedFoodRecord(null);
          setIsEditMode(false);
        }}
        foodRecord={selectedFoodRecord}
        onSave={handleSaveNutrition}
        currentDate={currentDate}
        initialEditMode={isEditMode}
      />
    </div>
  );
};

export default FoodRecordPage;
