import React from 'react';
import { createBrowserRouter, Navigate, useSearchParams } from 'react-router-dom';
import { useUserStore } from '@/domains/user/stores/userStore';
import { PageTransition } from '@/shared/components/animations';
import { LoadingScreen } from '@/shared/components/atoms';
import ProfileSetupPage from '@/app/pages/ProfileSetup';
import DashboardPage from '@/app/pages/Dashboard';
import CalendarPage from '@/app/pages/CalendarPage';

import NavigationDebugPage from '@/app/pages/NavigationDebugPage';
import ProfilePage from '@/app/pages/ProfilePage';
import FoodRecordPage from '@/app/pages/FoodRecord';


// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete, hydrated, profile } = useUserStore();

  console.log('=== ProtectedRoute 检查 ===');
  console.log('当前时间:', new Date().toISOString());
  console.log('hydrated:', hydrated);
  console.log('isProfileComplete:', isProfileComplete);
  console.log('profile存在:', !!profile);
  console.log('profile ID:', profile?.id);

  // 等待状态恢复完成
  if (!hydrated) {
    console.log('状态未恢复，显示加载屏幕');
    return <LoadingScreen message="正在加载用户数据..." />;
  }

  if (!isProfileComplete) {
    console.log('档案不完整，重定向到/setup');
    return <Navigate to="/setup" replace />;
  }

  console.log('档案完整，允许访问');
  return <>{children}</>;
};

// 设置页面守卫
const SetupRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isProfileComplete, hydrated, profile } = useUserStore();
  const [searchParams] = useSearchParams();
  const isEditMode = searchParams.get('mode') === 'edit';

  console.log('=== SetupRoute 检查 ===');
  console.log('当前时间:', new Date().toISOString());
  console.log('hydrated:', hydrated);
  console.log('isProfileComplete:', isProfileComplete);
  console.log('isEditMode:', isEditMode);
  console.log('profile存在:', !!profile);

  // 等待状态恢复完成
  if (!hydrated) {
    console.log('状态未恢复，显示加载屏幕');
    return <LoadingScreen message="正在加载用户数据..." />;
  }

  // 如果档案已完成且不是编辑模式，重定向到dashboard
  if (isProfileComplete && !isEditMode) {
    console.log('档案已完成且非编辑模式，重定向到/dashboard');
    return <Navigate to="/dashboard" replace />;
  }

  console.log('允许访问Setup页面');
  return <>{children}</>;
};

// 根路由重定向
const RootRedirect: React.FC = () => {
  const { isProfileComplete, hydrated, profile, debugProfile } = useUserStore();

  console.log('=== RootRedirect 检查 ===');
  console.log('当前时间:', new Date().toISOString());
  console.log('hydrated:', hydrated);
  console.log('isProfileComplete:', isProfileComplete);
  console.log('profile存在:', !!profile);

  // 等待状态恢复完成
  if (!hydrated) {
    console.log('状态未恢复，显示初始化屏幕');
    return <LoadingScreen message="正在初始化应用..." />;
  }

  // 调用调试方法输出详细信息
  debugProfile();

  const targetRoute = isProfileComplete ? "/dashboard" : "/setup";
  console.log('重定向到:', targetRoute);
  return <Navigate to={targetRoute} replace />;
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <RootRedirect />
  },
  {
    path: "/setup",
    element: (
      <SetupRoute>
        <PageTransition>
          <ProfileSetupPage />
        </PageTransition>
      </SetupRoute>
    )
  },
  {
    path: "/dashboard",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <DashboardPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/calendar",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <CalendarPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },

  {
    path: "/profile",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <ProfilePage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "/records",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <FoodRecordPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },

  {
    path: "/debug-nav",
    element: (
      <ProtectedRoute>
        <PageTransition>
          <NavigationDebugPage />
        </PageTransition>
      </ProtectedRoute>
    )
  },
  {
    path: "*",
    element: <Navigate to="/" replace />
  }
]);