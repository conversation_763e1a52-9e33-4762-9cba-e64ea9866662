import { useEffect } from 'react';
import { useUserStore } from '@/domains/user/stores/userStore';

/**
 * 用户状态初始化钩子
 * 确保在应用启动时正确初始化用户状态
 */
export const useUserInitialization = () => {
  const { hydrated, setHydrated } = useUserStore();

  useEffect(() => {
    // 如果状态还没有被标记为hydrated，则手动触发
    if (!hydrated) {
      // 检查localStorage中是否存在用户数据
      const userStorage = localStorage.getItem('user-profile-storage');
      
      if (userStorage) {
        try {
          const parsed = JSON.parse(userStorage);
          console.log('检测到已存在用户数据:', parsed);
          
          // 手动标记为已恢复
          setHydrated(true);
        } catch (error) {
          console.error('解析用户数据失败:', error);
          // 即使解析失败，也标记为已恢复以避免无限loading
          setHydrated(true);
        }
      } else {
        // 没有用户数据，直接标记为已恢复
        console.log('没有找到用户数据，标记为已恢复');
        setHydrated(true);
      }
    }
  }, [hydrated, setHydrated]);

  return { hydrated };
};

/**
 * 用户状态调试钩子
 * 开发环境下提供调试信息
 */
export const useUserDebug = () => {
  const userStore = useUserStore();

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('用户状态调试:', {
        hydrated: userStore.hydrated,
        isProfileComplete: userStore.isProfileComplete,
        hasProfile: !!userStore.profile,
        profile: userStore.profile
      });
    }
  }, [userStore.hydrated, userStore.isProfileComplete, userStore.profile]);

  return userStore;
};