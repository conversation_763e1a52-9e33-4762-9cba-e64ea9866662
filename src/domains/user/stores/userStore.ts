import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { UserProfile, CreateUserProfileForm, UpdateUserProfileForm, WeightUpdateForm, WeightRecord, MetabolicInfo } from '@/shared/types';
import { calculateNutritionPlan, calculateDynamicMetabolicInfo, calculateBMI, getBMIStatus, calculateBMR, calculateTDEE } from '@/shared/utils';
import { userRepository } from '@/infrastructure/storage';

interface UserState {
  // 状态
  profile: UserProfile | null;
  loading: boolean;
  error: string | null;
  hydrated: boolean; // 新增：标记状态是否已从localStorage恢复
  
  // 计算属性
  metabolicInfo: MetabolicInfo | null;
  readonly isProfileComplete: boolean; // 改为只读计算属性

  // 操作
  createProfile: (data: CreateUserProfileForm) => Promise<void>;
  updateProfile: (data: UpdateUserProfileForm) => Promise<void>;
  updateWeight: (data: WeightUpdateForm) => Promise<void>;
  refreshMetabolicInfo: () => Promise<void>;
  getWeightHistory: (days?: number) => WeightRecord[];
  getLatestWeightChange: () => { current: number; previous: number; change: number; changePercent: number } | null;
  clearProfile: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setHydrated: (hydrated: boolean) => void; // 新增
  clearAndReset: () => void; // 新增：清理并重置状态
}

// 创建动态代谢信息
function createMetabolicInfo(
  weight: number,
  height: number,
  age: number,
  gender: 'male' | 'female',
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'veryActive'
): MetabolicInfo {
  const dynamicInfo = calculateDynamicMetabolicInfo(weight, height, age, gender, activityLevel);
  return {
    ...dynamicInfo,
    lastUpdated: new Date()
  };
}

// 创建体重记录
function createWeightRecord(
  weight: number,
  height: number,
  age: number,
  gender: 'male' | 'female',
  activityLevel: 'sedentary' | 'light' | 'moderate' | 'active' | 'veryActive',
  note?: string,
  date?: Date,
  source: 'manual' | 'sync' | 'estimated' = 'manual'
): WeightRecord {
  const bmi = calculateBMI(weight, height);
  const bmr = calculateBMR({ weight, height, age, gender });
  const tdee = calculateTDEE(bmr, activityLevel);

  return {
    date: date || new Date(),
    weight,
    bmi,
    bmr,
    tdee,
    note,
    source
  };
}

// 迁移旧版本用户档案
function migrateProfile(profile: any): UserProfile {
  // 安全检查
  if (!profile) {
    throw new Error('Profile is null or undefined');
  }

  // 确保基本属性存在
  const requiredFields = ['weight', 'height', 'age', 'gender'];
  for (const field of requiredFields) {
    if (!profile[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // 如果已经有新字段，直接返回
  if (profile.weightHistory && profile.metabolicInfo) {
    return profile as UserProfile;
  }

  // 创建缺失的字段
  const weightHistory: WeightRecord[] = profile.weightHistory || [
    createWeightRecord(
      profile.weight,
      profile.height,
      profile.age,
      profile.gender,
      profile.activityLevel || 'moderate',
      '历史记录',
      profile.createdAt || new Date(),
      'manual'
    )
  ];

  const metabolicInfo: MetabolicInfo = profile.metabolicInfo || createMetabolicInfo(
    profile.weight,
    profile.height,
    profile.age,
    profile.gender,
    profile.activityLevel || 'moderate'
  );

  return {
    ...profile,
    weightHistory,
    metabolicInfo
  };
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // 初始状态
      profile: null,
      loading: false,
      error: null,
      hydrated: false, // 初始化为false
      
      // 计算属性 - 动态获取metabolicInfo
      get metabolicInfo() {
        const state = get();
        // 确保状态已经恢复且profile存在
        if (!state || !state.hydrated || !state.profile) {
          return null;
        }
        try {
          const migratedProfile = migrateProfile(state.profile);
          return migratedProfile.metabolicInfo;
        } catch (error) {
          console.error('获取metabolicInfo失败:', error);
          return null;
        }
      },

      // 计算属性 - 动态计算档案完整性
      get isProfileComplete() {
        try {
          const state = get();
          console.log('=== isProfileComplete 计算 ===');
          console.log('state存在:', !!state);
          console.log('hydrated:', state?.hydrated);
          console.log('profile存在:', !!state?.profile);

          // 确保状态已经恢复且profile存在
          console.log('条件检查详情:');
          console.log('  !state:', !state);
          console.log('  !state.hydrated:', !state?.hydrated);
          console.log('  !state.profile:', !state?.profile);
          console.log('  整体条件:', !state || !state?.hydrated || !state?.profile);

          if (!state || !state?.hydrated || !state?.profile) {
            console.log('isProfileComplete: false (状态未恢复或profile不存在)');
            return false;
          }
        } catch (error) {
          console.error('isProfileComplete计算错误:', error);
          return false;
        }
        const profile = state.profile;
        
        // 检查必需字段
        const requiredFields = [
          'name', 'weight', 'height', 'age', 'gender',
          'targetWeight', 'targetDays', 'activityLevel'
        ];

        console.log('检查必需字段:', requiredFields);
        const fieldChecks = requiredFields.map(field => {
          const value = profile[field as keyof UserProfile];
          const isValid = value !== null && value !== undefined && value !== '';
          console.log(`字段 ${field}:`, value, '有效:', isValid);
          return isValid;
        });

        const isComplete = fieldChecks.every(check => check);
        console.log('isProfileComplete 结果:', isComplete);
        return isComplete;
      },

      // 创建用户档案
      createProfile: async (data: CreateUserProfileForm) => {
        console.log('=== userStore.createProfile 开始 ===');
        console.log('输入数据:', data);

        try {
          console.log('设置loading状态...');
          set({ loading: true, error: null });

          // 计算BMR和营养计划
          console.log('计算营养计划...');
          const nutritionPlan = calculateNutritionPlan({
            weight: data.weight,
            height: data.height,
            age: data.age,
            gender: data.gender,
            targetWeight: data.targetWeight,
            targetDays: data.targetDays,
            activityLevel: data.activityLevel
          });
          console.log('营养计划计算完成:', nutritionPlan);

          // 创建初始代谢信息
          const metabolicInfo = createMetabolicInfo(
            data.weight,
            data.height,
            data.age,
            data.gender,
            data.activityLevel
          );

          // 创建初始体重记录
          const initialWeightRecord = createWeightRecord(
            data.weight,
            data.height,
            data.age,
            data.gender,
            data.activityLevel,
            '初始体重记录',
            new Date(),
            'manual'
          );

          // 创建完整的用户档案
          const profile: UserProfile = {
            id: `user_${Date.now()}`,
            createdAt: new Date(),
            updatedAt: new Date(),
            
            // 基本信息
            name: data.name,
            height: data.height,
            weight: data.weight,
            age: data.age,
            gender: data.gender,
            
            // 目标设置
            targetWeight: data.targetWeight,
            targetDays: data.targetDays,
            activityLevel: data.activityLevel,
            
            // 计算结果
            bmr: nutritionPlan.bmr,
            tdee: nutritionPlan.tdee,
            dailyCalorieLimit: nutritionPlan.dailyCalorieLimit,
            
            // 默认三餐分配比例
            mealRatios: {
              breakfast: 0.3,
              lunch: 0.4,
              dinner: 0.3
            },

            // 体重历史记录
            weightHistory: [initialWeightRecord],

            // 动态代谢信息
            metabolicInfo,
            
            // 默认偏好设置
            preferences: {
              theme: 'system',
              language: 'zh-CN',
              notifications: {
                mealReminders: true,
                dailySummary: true,
                weeklyReport: false
              },
              units: {
                weight: 'kg',
                height: 'cm'
              }
            }
          };

          console.log('设置profile到store...');
          set({
            profile,
            loading: false
          });

          console.log('=== userStore.createProfile 完成 ===');
          console.log('创建的profile:', profile);

          // 验证状态
          const newState = get();
          console.log('新状态验证:', {
            hasProfile: !!newState.profile,
            isProfileComplete: newState.isProfileComplete,
            hydrated: newState.hydrated,
            profileId: newState.profile?.id
          });

        } catch (error) {
          console.error('=== userStore.createProfile 失败 ===');
          console.error('错误详情:', error);
          const errorMessage = error instanceof Error ? error.message : '创建档案失败';
          set({
            error: errorMessage,
            loading: false
          });
          throw error;
        }
      },

      // 更新用户档案
      updateProfile: async (data: UpdateUserProfileForm) => {
        try {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('用户档案不存在');
          }

          set({ loading: true, error: null });

          // 迁移旧版本档案
          const migratedProfile = migrateProfile(currentProfile);

          // 如果更新了影响BMR计算的字段，重新计算
          let updatedProfile: UserProfile = { 
            ...migratedProfile, 
            ...data, 
            updatedAt: new Date(),
            // 合并preferences，确保保持完整性
            preferences: data.preferences ? { ...migratedProfile.preferences, ...data.preferences } : migratedProfile.preferences
          };

          const needsRecalculation = 
            data.weight !== undefined ||
            data.height !== undefined ||
            data.age !== undefined ||
            data.gender !== undefined ||
            data.targetWeight !== undefined ||
            data.targetDays !== undefined ||
            data.activityLevel !== undefined;

          if (needsRecalculation) {
            const nutritionPlan = calculateNutritionPlan({
              weight: updatedProfile.weight,
              height: updatedProfile.height,
              age: updatedProfile.age,
              gender: updatedProfile.gender,
              targetWeight: updatedProfile.targetWeight,
              targetDays: updatedProfile.targetDays,
              activityLevel: updatedProfile.activityLevel
            });

            // 重新计算代谢信息
            const metabolicInfo = createMetabolicInfo(
              updatedProfile.weight,
              updatedProfile.height,
              updatedProfile.age,
              updatedProfile.gender,
              updatedProfile.activityLevel
            );

            updatedProfile = {
              ...updatedProfile,
              bmr: nutritionPlan.bmr,
              tdee: nutritionPlan.tdee,
              dailyCalorieLimit: nutritionPlan.dailyCalorieLimit,
              metabolicInfo
            };

            // 如果体重发生变化，添加体重记录
            if (data.weight !== undefined && data.weight !== migratedProfile.weight) {
              const newWeightRecord = createWeightRecord(
                updatedProfile.weight,
                updatedProfile.height,
                updatedProfile.age,
                updatedProfile.gender,
                updatedProfile.activityLevel,
                '档案更新',
                new Date(),
                'manual'
              );

              updatedProfile.weightHistory = [
                ...migratedProfile.weightHistory,
                newWeightRecord
              ];
            }
          }

          set({ 
            profile: updatedProfile, 
            loading: false 
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新档案失败';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw error;
        }
      },

      // 更新体重
      updateWeight: async (data: WeightUpdateForm) => {
        try {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('用户档案不存在');
          }

          set({ loading: true, error: null });

          // 迁移旧版本档案
          const migratedProfile = migrateProfile(currentProfile);

          // 创建新的体重记录
          const newWeightRecord = createWeightRecord(
            data.weight,
            migratedProfile.height,
            migratedProfile.age,
            migratedProfile.gender,
            migratedProfile.activityLevel,
            data.note,
            data.date,
            'manual'
          );

          // 重新计算营养计划
          const nutritionPlan = calculateNutritionPlan({
            weight: data.weight,
            height: migratedProfile.height,
            age: migratedProfile.age,
            gender: migratedProfile.gender,
            targetWeight: migratedProfile.targetWeight,
            targetDays: migratedProfile.targetDays,
            activityLevel: migratedProfile.activityLevel
          });

          // 重新计算代谢信息
          const metabolicInfo = createMetabolicInfo(
            data.weight,
            migratedProfile.height,
            migratedProfile.age,
            migratedProfile.gender,
            migratedProfile.activityLevel
          );

          // 更新档案
          const updatedProfile: UserProfile = {
            ...migratedProfile,
            weight: data.weight,
            bmr: nutritionPlan.bmr,
            tdee: nutritionPlan.tdee,
            dailyCalorieLimit: nutritionPlan.dailyCalorieLimit,
            metabolicInfo,
            weightHistory: [...migratedProfile.weightHistory, newWeightRecord],
            updatedAt: new Date()
          };

          set({ 
            profile: updatedProfile, 
            loading: false 
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新体重失败';
          set({ 
            error: errorMessage, 
            loading: false 
          });
          throw error;
        }
      },

      // 刷新代谢信息
      refreshMetabolicInfo: async () => {
        try {
          const currentProfile = get().profile;
          if (!currentProfile) {
            throw new Error('用户档案不存在');
          }

          // 迁移旧版本档案
          const migratedProfile = migrateProfile(currentProfile);

          const metabolicInfo = createMetabolicInfo(
            migratedProfile.weight,
            migratedProfile.height,
            migratedProfile.age,
            migratedProfile.gender,
            migratedProfile.activityLevel
          );

          set({ 
            profile: {
              ...migratedProfile,
              metabolicInfo,
              updatedAt: new Date()
            }
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '刷新代谢信息失败';
          set({ error: errorMessage });
          throw error;
        }
      },

      // 获取体重历史记录
      getWeightHistory: (days?: number) => {
        const currentProfile = get().profile;
        if (!currentProfile) {
          return [];
        }

        // 迁移旧版本档案
        const migratedProfile = migrateProfile(currentProfile);

        if (!migratedProfile.weightHistory) {
          return [];
        }

        let history = [...migratedProfile.weightHistory].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );

        if (days) {
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          history = history.filter(record => new Date(record.date) >= cutoffDate);
        }

        return history;
      },

      // 获取最新的体重变化
      getLatestWeightChange: () => {
        const currentProfile = get().profile;
        if (!currentProfile) {
          return null;
        }

        // 迁移旧版本档案
        const migratedProfile = migrateProfile(currentProfile);

        if (!migratedProfile.weightHistory || migratedProfile.weightHistory.length < 2) {
          return null;
        }

        const sortedHistory = [...migratedProfile.weightHistory].sort((a, b) => 
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );

        const current = sortedHistory[0].weight;
        const previous = sortedHistory[1].weight;
        const change = Math.round((current - previous) * 10) / 10;
        const changePercent = Math.round((change / previous) * 1000) / 10;

        return {
          current,
          previous,
          change,
          changePercent
        };
      },

      // 清除用户档案
      clearProfile: () => {
        set({ 
          profile: null, 
          error: null 
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      },

      // 设置hydrated状态
      setHydrated: (hydrated: boolean) => {
        set({ hydrated });
      },

      // 调试方法 - 输出profile详细信息
      debugProfile: () => {
        const state = get();
        console.log('=== Profile 调试信息 ===');
        console.log('完整profile对象:', state.profile);
        if (state.profile) {
          const requiredFields = [
            'name', 'weight', 'height', 'age', 'gender',
            'targetWeight', 'targetDays', 'activityLevel'
          ];
          requiredFields.forEach(field => {
            const value = state.profile![field as keyof UserProfile];
            console.log(`${field}:`, value, typeof value, value === null ? 'NULL' : value === undefined ? 'UNDEFINED' : value === '' ? 'EMPTY' : 'OK');
          });
        }
        console.log('isProfileComplete:', state.isProfileComplete);
        console.log('========================');
      },

      // 清理并重置状态
      clearAndReset: () => {
        console.log('清理并重置用户状态');
        // 清除localStorage
        localStorage.removeItem('user-profile-storage');
        // 重置状态
        set({
          profile: null,
          loading: false,
          error: null,
          hydrated: true
        });
      }
    }),
    {
      name: 'user-profile-storage',
      partialize: (state) => ({ 
        profile: state.profile 
      }),
      // 添加迁移逻辑
      migrate: (persistedState: any, version: number) => {
        console.log('正在迁移用户状态，版本:', version);
        try {
          if (persistedState && persistedState.profile) {
            persistedState.profile = migrateProfile(persistedState.profile);
          }
          return persistedState;
        } catch (error) {
          console.error('状态迁移失败:', error);
          // 返回安全的默认状态
          return {
            profile: null
          };
        }
      },
      onRehydrateStorage: () => {
        // 返回一个回调函数，在状态恢复完成后调用
        return (state, error) => {
          console.log('=== onRehydrateStorage 回调 ===');
          console.log('error:', error);
          console.log('state:', state);
          console.log('state.profile:', state?.profile);

          if (error) {
            console.error('用户状态恢复失败:', error);
            // 即使出错也要标记为已恢复，避免无限loading
            if (state) {
              state.hydrated = true;
            }
          } else {
            console.log('用户状态恢复成功', state?.profile ? '有用户数据' : '无用户数据');
            if (state?.profile) {
              console.log('恢复的profile详情:', state.profile);
            }
          }

          // 标记状态已经恢复
          if (state) {
            // 直接设置状态，避免调用方法可能的问题
            state.hydrated = true;
            console.log('状态恢复完成，hydrated设置为true');
          }
          console.log('=== onRehydrateStorage 完成 ===');
        };
      },
      version: 1
    }
  )
);