/**
 * 运动记录状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface ExerciseRecord {
  id: string;
  name: string;
  type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'other';
  duration: number; // 分钟
  intensity: 'low' | 'medium' | 'high';
  caloriesBurned: number;
  recordedAt: Date;
  confidence?: number;
  dataSource?: 'manual_input' | 'ai_recognition' | 'image_analysis';
  isEdited?: boolean;
  notes?: string;
}

export interface DailyExerciseRecords {
  date: string; // YYYY-MM-DD格式
  records: ExerciseRecord[];
  totalDuration: number;
  totalCaloriesBurned: number;
}

interface ExerciseState {
  // 状态
  dailyRecords: Record<string, DailyExerciseRecords>;
  loading: boolean;
  error: string | null;

  // 操作
  addExerciseRecord: (date: Date, record: Omit<ExerciseRecord, 'id'>) => void;
  updateExerciseRecord: (date: Date, recordId: string, updates: Partial<ExerciseRecord>) => void;
  deleteExerciseRecord: (date: Date, recordId: string) => void;
  getDailyExerciseRecords: (date: Date) => DailyExerciseRecords;
  getWeeklyStats: (startDate: Date) => {
    totalDuration: number;
    totalCalories: number;
    exerciseCount: number;
    averageIntensity: string;
  };
  clearAllRecords: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 辅助函数：格式化日期为字符串
const formatDateKey = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// 辅助函数：生成唯一ID
const generateId = (): string => {
  return `exercise_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// 辅助函数：计算每日统计
const calculateDailyStats = (records: ExerciseRecord[]): { totalDuration: number; totalCaloriesBurned: number } => {
  return records.reduce(
    (acc, record) => ({
      totalDuration: acc.totalDuration + record.duration,
      totalCaloriesBurned: acc.totalCaloriesBurned + record.caloriesBurned
    }),
    { totalDuration: 0, totalCaloriesBurned: 0 }
  );
};

export const useExerciseStore = create<ExerciseState>()(
  persist(
    (set, get) => ({
      // 初始状态
      dailyRecords: {},
      loading: false,
      error: null,

      // 添加运动记录
      addExerciseRecord: (date: Date, record: Omit<ExerciseRecord, 'id'>) => {
        try {
          set({ loading: true, error: null });

          const dateKey = formatDateKey(date);
          const newRecord: ExerciseRecord = {
            ...record,
            id: generateId(),
            recordedAt: date
          };

          const state = get();
          const existingDay = state.dailyRecords[dateKey] || {
            date: dateKey,
            records: [],
            totalDuration: 0,
            totalCaloriesBurned: 0
          };

          const updatedRecords = [...existingDay.records, newRecord];
          const stats = calculateDailyStats(updatedRecords);

          const updatedDay: DailyExerciseRecords = {
            ...existingDay,
            records: updatedRecords,
            totalDuration: stats.totalDuration,
            totalCaloriesBurned: stats.totalCaloriesBurned
          };

          set({
            dailyRecords: {
              ...state.dailyRecords,
              [dateKey]: updatedDay
            },
            loading: false
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '添加运动记录失败';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      // 更新运动记录
      updateExerciseRecord: (date: Date, recordId: string, updates: Partial<ExerciseRecord>) => {
        try {
          set({ loading: true, error: null });

          const dateKey = formatDateKey(date);
          const state = get();
          const dayRecords = state.dailyRecords[dateKey];

          if (!dayRecords) {
            throw new Error('未找到指定日期的运动记录');
          }

          const updatedRecords = dayRecords.records.map(record =>
            record.id === recordId
              ? { ...record, ...updates, isEdited: true }
              : record
          );

          const stats = calculateDailyStats(updatedRecords);

          const updatedDay: DailyExerciseRecords = {
            ...dayRecords,
            records: updatedRecords,
            totalDuration: stats.totalDuration,
            totalCaloriesBurned: stats.totalCaloriesBurned
          };

          set({
            dailyRecords: {
              ...state.dailyRecords,
              [dateKey]: updatedDay
            },
            loading: false
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '更新运动记录失败';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      // 删除运动记录
      deleteExerciseRecord: (date: Date, recordId: string) => {
        try {
          set({ loading: true, error: null });

          const dateKey = formatDateKey(date);
          const state = get();
          const dayRecords = state.dailyRecords[dateKey];

          if (!dayRecords) {
            throw new Error('未找到指定日期的运动记录');
          }

          const updatedRecords = dayRecords.records.filter(record => record.id !== recordId);
          const stats = calculateDailyStats(updatedRecords);

          const updatedDay: DailyExerciseRecords = {
            ...dayRecords,
            records: updatedRecords,
            totalDuration: stats.totalDuration,
            totalCaloriesBurned: stats.totalCaloriesBurned
          };

          set({
            dailyRecords: {
              ...state.dailyRecords,
              [dateKey]: updatedDay
            },
            loading: false
          });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '删除运动记录失败';
          set({ error: errorMessage, loading: false });
          throw error;
        }
      },

      // 获取每日运动记录
      getDailyExerciseRecords: (date: Date): DailyExerciseRecords => {
        const dateKey = formatDateKey(date);
        const state = get();
        return state.dailyRecords[dateKey] || {
          date: dateKey,
          records: [],
          totalDuration: 0,
          totalCaloriesBurned: 0
        };
      },

      // 获取周统计
      getWeeklyStats: (startDate: Date) => {
        const state = get();
        let totalDuration = 0;
        let totalCalories = 0;
        let exerciseCount = 0;
        let intensitySum = 0;

        // 计算一周的数据
        for (let i = 0; i < 7; i++) {
          const date = new Date(startDate);
          date.setDate(startDate.getDate() + i);
          const dateKey = formatDateKey(date);
          const dayRecords = state.dailyRecords[dateKey];

          if (dayRecords) {
            totalDuration += dayRecords.totalDuration;
            totalCalories += dayRecords.totalCaloriesBurned;
            exerciseCount += dayRecords.records.length;

            // 计算强度平均值
            dayRecords.records.forEach(record => {
              const intensityValue = record.intensity === 'low' ? 1 : record.intensity === 'medium' ? 2 : 3;
              intensitySum += intensityValue;
            });
          }
        }

        const averageIntensityValue = exerciseCount > 0 ? intensitySum / exerciseCount : 0;
        const averageIntensity = averageIntensityValue <= 1.5 ? 'low' : averageIntensityValue <= 2.5 ? 'medium' : 'high';

        return {
          totalDuration,
          totalCalories,
          exerciseCount,
          averageIntensity
        };
      },

      // 清除所有记录
      clearAllRecords: () => {
        set({
          dailyRecords: {},
          error: null
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      }
    }),
    {
      name: 'exercise-records-storage',
      partialize: (state) => ({
        dailyRecords: state.dailyRecords
      })
    }
  )
);
