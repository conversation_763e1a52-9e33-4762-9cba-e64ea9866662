import { BMRCalculationParams, BMRCalculationResult } from '@/shared/types';

/**
 * 基于Mifflin-St Jeor公式计算BMR
 * 男性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 + 5
 * 女性：BMR = 10 × 体重(kg) + 6.25 × 身高(cm) - 5 × 年龄 - 161
 */
export function calculateBMR(params: BMRCalculationParams): number {
  const { weight, height, age, gender } = params;

  const baseBMR = 10 * weight + 6.25 * height - 5 * age;
  const bmr = gender === 'male' ? baseBMR + 5 : baseBMR - 161;
  // 返回整数，避免小数点显示问题
  return Math.round(bmr);
}

/**
 * 活动水平系数
 */
export const ACTIVITY_MULTIPLIERS = {
  sedentary: 1.2,      // 久坐不动
  light: 1.375,        // 轻度活动
  moderate: 1.55,      // 中度活动
  active: 1.725,       // 高度活动
  veryActive: 1.9      // 极高活动
} as const;

/**
 * 计算TDEE (Total Daily Energy Expenditure)
 */
export function calculateTDEE(bmr: number, activityLevel: keyof typeof ACTIVITY_MULTIPLIERS): number {
  const tdee = bmr * ACTIVITY_MULTIPLIERS[activityLevel];
  // 返回整数，避免小数点显示问题
  return Math.round(tdee);
}

/**
 * 计算每日卡路里限额（基于减重目标）
 */
export function calculateDailyCalorieLimit(
  tdee: number,
  targetWeightLoss: number, // kg
  targetDays: number
): number {
  // 1kg脂肪 ≈ 7700卡路里
  const totalCalorieDeficit = targetWeightLoss * 7700;
  const dailyDeficit = totalCalorieDeficit / targetDays;
  
  // 确保每日卡路里不低于BMR的80%（安全下限）
  const minCalories = tdee * 0.8;
  const targetCalories = tdee - dailyDeficit;
  
  // 使用 Math.round 确保返回整数，避免小数点显示问题
  return Math.round(Math.max(targetCalories, minCalories));
}

/**
 * 计算三餐卡路里分配
 */
export function calculateMealCalories(
  dailyLimit: number,
  ratios: { breakfast: number; lunch: number; dinner: number }
): { breakfast: number; lunch: number; dinner: number } {
  return {
    breakfast: Math.round(dailyLimit * ratios.breakfast),
    lunch: Math.round(dailyLimit * ratios.lunch),
    dinner: Math.round(dailyLimit * ratios.dinner)
  };
}

/**
 * 完整的BMR和卡路里计算
 */
export function calculateNutritionPlan(
  params: BMRCalculationParams & {
    targetWeight: number;
    targetDays: number;
    activityLevel: keyof typeof ACTIVITY_MULTIPLIERS;
    mealRatios?: { breakfast: number; lunch: number; dinner: number };
  }
): BMRCalculationResult {
  const bmr = calculateBMR(params);
  const tdee = calculateTDEE(bmr, params.activityLevel);
  const weightLoss = params.weight - params.targetWeight;
  const dailyCalorieLimit = calculateDailyCalorieLimit(tdee, weightLoss, params.targetDays);
  
  // 默认三餐比例
  const defaultRatios = { breakfast: 0.3, lunch: 0.4, dinner: 0.3 };
  const ratios = params.mealRatios || defaultRatios;
  
  const mealCalories = calculateMealCalories(dailyCalorieLimit, ratios);
  
  // 计算预期减重速度（kg/week）
  const weeklyDeficit = (tdee - dailyCalorieLimit) * 7;
  const weightLossRate = weeklyDeficit / 7700;
  
  return {
    bmr,
    tdee,
    dailyCalorieLimit,
    weightLossRate,
    mealCalories
  };
}

/**
 * 计算推荐体重（与ProfileSetupForm中的算法保持一致）
 */
export function calculateRecommendedWeight(
  currentWeight: number,
  height: number,
  targetDays: number,
  activityLevel: keyof typeof ACTIVITY_MULTIPLIERS
): number {
  const heightInM = height / 100;
  const currentBMI = currentWeight / (heightInM * heightInM);

  // 健康BMI范围：18.5-24.9
  const healthyMinWeight = 18.5 * heightInM * heightInM;
  const healthyMaxWeight = 24.9 * heightInM * heightInM;

  // 活动水平系数（影响减重潜力）
  const activityMultipliers = {
    sedentary: 0.8,     // 久坐：较低减重潜力
    light: 1.0,         // 轻度活动：标准减重潜力
    moderate: 1.2,      // 中度活动：较好减重潜力
    active: 1.4,        // 高度活动：很好减重潜力
    veryActive: 1.6     // 极高活动：最佳减重潜力
  };

  const activityMultiplier = activityMultipliers[activityLevel] || 1.0;

  // 基于目标天数和活动水平计算合理减重量
  const weeksAvailable = targetDays / 7;
  const baseWeeklyLoss = 0.5; // 基础每周减重0.5kg
  const adjustedWeeklyLoss = baseWeeklyLoss * activityMultiplier;
  const totalPossibleLoss = weeksAvailable * adjustedWeeklyLoss;

  // 计算推荐目标体重
  let recommendedWeight: number;

  if (currentBMI > 24.9) {
    // 超重：基于活动水平和时间计算合理目标
    const idealReduction = currentWeight - healthyMaxWeight;
    const timeBasedReduction = Math.min(totalPossibleLoss, idealReduction);
    recommendedWeight = currentWeight - Math.max(timeBasedReduction, 2); // 至少减重2kg
  } else if (currentBMI < 18.5) {
    // 体重不足：推荐增到健康范围
    recommendedWeight = healthyMinWeight;
  } else {
    // 健康范围内：基于活动水平适度优化
    const moderateReduction = Math.min(totalPossibleLoss * 0.7, 5); // 最多减5kg
    recommendedWeight = Math.max(currentWeight - moderateReduction, healthyMinWeight);
  }

  // 确保推荐体重在合理范围内
  recommendedWeight = Math.max(recommendedWeight, healthyMinWeight);
  recommendedWeight = Math.min(recommendedWeight, currentWeight); // 不超过当前体重

  // 保持2位小数精度
  return Math.round(recommendedWeight * 100) / 100;
}

/**
 * 计算BMI指数
 */
export function calculateBMI(weight: number, height: number): number {
  const heightInM = height / 100;
  const bmi = weight / (heightInM * heightInM);
  return Math.round(bmi * 10) / 10; // 保留1位小数
}

/**
 * 获取BMI健康状态
 */
export function getBMIStatus(bmi: number): {
  category: string;
  color: string;
  description: string;
  recommendation: string;
} {
  if (bmi < 18.5) {
    return {
      category: '偏瘦',
      color: 'text-blue-600',
      description: '体重不足',
      recommendation: '建议增加营养摄入，适量运动增肌'
    };
  } else if (bmi < 24) {
    return {
      category: '正常',
      color: 'text-green-600',
      description: '健康体重',
      recommendation: '保持良好的饮食和运动习惯'
    };
  } else if (bmi < 28) {
    return {
      category: '偏胖',
      color: 'text-yellow-600',
      description: '超重',
      recommendation: '建议控制饮食，增加有氧运动'
    };
  } else {
    return {
      category: '肥胖',
      color: 'text-red-600',
      description: '肥胖',
      recommendation: '建议咨询专业医生，制定减重计划'
    };
  }
}

/**
 * 计算动态代谢信息
 */
export function calculateDynamicMetabolicInfo(
  weight: number,
  height: number,
  age: number,
  gender: 'male' | 'female',
  activityLevel: keyof typeof ACTIVITY_MULTIPLIERS
): {
  bmi: number;
  bmiStatus: ReturnType<typeof getBMIStatus>;
  bmr: number;
  tdee: number;
  healthyWeightRange: { min: number; max: number };
  weightStatus: {
    category: 'underweight' | 'normal' | 'overweight' | 'obese';
    deviation: number; // 与健康体重中位数的偏差
  };
} {
  // 计算BMI
  const bmi = calculateBMI(weight, height);
  const bmiStatus = getBMIStatus(bmi);

  // 计算BMR
  const bmr = calculateBMR({ weight, height, age, gender });

  // 计算TDEE
  const tdee = calculateTDEE(bmr, activityLevel);

  // 计算健康体重范围
  const heightInM = height / 100;
  const healthyWeightRange = {
    min: Math.round(18.5 * heightInM * heightInM * 10) / 10,
    max: Math.round(24.9 * heightInM * heightInM * 10) / 10
  };

  // 计算体重状态
  const healthyWeightMid = (healthyWeightRange.min + healthyWeightRange.max) / 2;
  const deviation = Math.round((weight - healthyWeightMid) * 10) / 10;
  
  let weightCategory: 'underweight' | 'normal' | 'overweight' | 'obese';
  if (bmi < 18.5) weightCategory = 'underweight';
  else if (bmi < 24) weightCategory = 'normal';
  else if (bmi < 28) weightCategory = 'overweight';
  else weightCategory = 'obese';

  return {
    bmi,
    bmiStatus,
    bmr,
    tdee,
    healthyWeightRange,
    weightStatus: {
      category: weightCategory,
      deviation
    }
  };
}

/**
 * 计算体重变化对代谢的影响
 */
export function calculateMetabolicImpact(
  currentWeight: number,
  previousWeight: number,
  height: number,
  age: number,
  gender: 'male' | 'female',
  activityLevel: keyof typeof ACTIVITY_MULTIPLIERS
): {
  weightChange: number;
  bmiChange: number;
  bmrChange: number;
  tdeeChange: number;
  weeklyRateEstimate: number;
  healthImpact: {
    category: 'positive' | 'neutral' | 'concerning';
    message: string;
  };
} {
  const weightChange = Math.round((currentWeight - previousWeight) * 10) / 10;
  
  // 计算BMI变化
  const currentBMI = calculateBMI(currentWeight, height);
  const previousBMI = calculateBMI(previousWeight, height);
  const bmiChange = Math.round((currentBMI - previousBMI) * 10) / 10;

  // 计算BMR变化
  const currentBMR = calculateBMR({ weight: currentWeight, height, age, gender });
  const previousBMR = calculateBMR({ weight: previousWeight, height, age, gender });
  const bmrChange = currentBMR - previousBMR;

  // 计算TDEE变化
  const currentTDEE = calculateTDEE(currentBMR, activityLevel);
  const previousTDEE = calculateTDEE(previousBMR, activityLevel);
  const tdeeChange = currentTDEE - previousTDEE;

  // 估算每周变化率（假设7天的变化）
  const weeklyRateEstimate = Math.round((weightChange / 7) * 7 * 10) / 10;

  // 评估健康影响
  let healthImpact: { category: 'positive' | 'neutral' | 'concerning'; message: string };
  
  if (Math.abs(weightChange) < 0.5) {
    healthImpact = {
      category: 'neutral',
      message: '体重变化较小，继续保持'
    };
  } else if (weightChange < 0) {
    // 体重减少
    if (previousBMI > 24 && Math.abs(weeklyRateEstimate) <= 1.0) {
      healthImpact = {
        category: 'positive',
        message: '健康的减重速度，继续保持'
      };
    } else if (Math.abs(weeklyRateEstimate) > 1.5) {
      healthImpact = {
        category: 'concerning',
        message: '减重速度过快，注意营养均衡'
      };
    } else {
      healthImpact = {
        category: 'neutral',
        message: '体重在正常范围内变化'
      };
    }
  } else {
    // 体重增加
    if (previousBMI < 18.5 && weeklyRateEstimate <= 0.5) {
      healthImpact = {
        category: 'positive',
        message: '健康的增重，有利于身体恢复'
      };
    } else if (previousBMI > 24 || weeklyRateEstimate > 1.0) {
      healthImpact = {
        category: 'concerning',
        message: '体重增加较快，建议控制饮食'
      };
    } else {
      healthImpact = {
        category: 'neutral',
        message: '体重在正常范围内变化'
      };
    }
  }

  return {
    weightChange,
    bmiChange,
    bmrChange,
    tdeeChange,
    weeklyRateEstimate,
    healthImpact
  };
}

/**
 * 验证减重目标是否安全（修改为不阻止保存，与推荐体重一致时跳过验证）
 * 集成AURA-X协议：基于权威健康指导原则进行验证
 */
export function validateWeightLossGoal(
  currentWeight: number,
  targetWeight: number,
  targetDays: number,
  height?: number,
  activityLevel?: keyof typeof ACTIVITY_MULTIPLIERS
): { isValid: boolean; message?: string; isWarning?: boolean } {
  // 如果提供了身高和活动水平，检查是否与推荐体重一致
  if (height && activityLevel) {
    const recommendedWeight = calculateRecommendedWeight(currentWeight, height, targetDays, activityLevel);
    // 如果目标体重与推荐体重一致（允许0.1kg的误差），直接跳过验证
    if (Math.abs(targetWeight - recommendedWeight) <= 0.1) {
      return { isValid: true };
    }
  }

  const weightLoss = currentWeight - targetWeight;
  const weeklyLoss = (weightLoss / targetDays) * 7;

  if (weightLoss <= 0) {
    return {
      isValid: true, // 修改为不阻止保存
      isWarning: true,
      message: '目标体重应低于当前体重'
    };
  }

  // 所有验证都改为警告级别，不阻止保存
  if (weeklyLoss > 1.2) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度过快，建议每周减重不超过1.2kg'
    };
  }

  if (weeklyLoss < 0.1) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度过慢，建议每周减重至少0.1kg'
    };
  }

  // 添加警告级别的提示（不阻止用户继续）
  if (weeklyLoss > 1.0) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度较快，请注意营养均衡和身体状况'
    };
  }

  if (weeklyLoss < 0.3) {
    return {
      isValid: true,
      isWarning: true,
      message: '减重速度较慢，可以适当增加运动量'
    };
  }

  return { isValid: true };
}