import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { UnifiedFoodRecordingModal } from '../modals/UnifiedFoodRecordingModal';
import { ExerciseRecordingModal } from '../modals/ExerciseRecordingModal';

interface BottomNavigationProps {
  showAddButton?: boolean;
  onRecognitionComplete?: (result: any) => void;
  onExerciseRecordComplete?: (result: any) => void;
  currentDate?: Date;
  isAIProcessing?: boolean; // 添加AI处理状态
  onProcessingStateChange?: (isProcessing: boolean) => void; // 添加处理状态回调
}

const BottomNavigation: React.FC<BottomNavigationProps> = ({
  showAddButton = true,
  onRecognitionComplete,
  onExerciseRecordComplete,
  currentDate,
  isAIProcessing = false,
  onProcessingStateChange
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showFoodModal, setShowFoodModal] = useState(false);
  const [showExerciseModal, setShowExerciseModal] = useState(false);

  const isActive = (path: string) => location.pathname === path;



  // 处理食物记录按钮点击
  const handleFoodRecord = () => {
    setShowFoodModal(true);
    // 关闭下拉菜单
    const dropdown = document.activeElement as HTMLElement;
    dropdown?.blur();
  };

  // 处理运动记录按钮点击
  const handleExerciseRecord = () => {
    setShowExerciseModal(true);
    // 关闭下拉菜单
    const dropdown = document.activeElement as HTMLElement;
    dropdown?.blur();
  };

  // 处理食物记录完成
  const handleFoodRecordComplete = (result: any) => {
    onRecognitionComplete?.(result);
    setShowFoodModal(false);
  };

  // 处理运动记录完成
  const handleExerciseRecordComplete = (result: any) => {
    onExerciseRecordComplete?.(result);
    setShowExerciseModal(false);
  };



  return (
    <>
      <div
        className="dock fixed bottom-0 left-0 right-0 bg-base-100 xl:bg-base-100/95 xl:backdrop-blur-sm"
        style={{
          position: 'fixed',
          zIndex: 1000,
          transform: 'none'
        }}
      >
        {/* 首页 */}
        <button
          onClick={() => navigate('/dashboard')}
          className={`${isActive('/dashboard') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="首页"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">🏠</span>
          <span className="dock-label text-xs xl:text-sm">首页</span>
        </button>

        {/* 记录 */}
        <button
          onClick={() => navigate('/records')}
          className={`${isActive('/records') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="记录"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">📋</span>
          <span className="dock-label text-xs xl:text-sm">记录</span>
        </button>

        {/* 添加按钮 */}
        {showAddButton && (
          <div className={`dropdown dropdown-top dropdown-center ${isAIProcessing ? 'pointer-events-none' : ''}`}>
            <button
              tabIndex={0}
              role="button"
              aria-label="添加记录"
              disabled={isAIProcessing}
              style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
            >
              <span className="text-lg">➕</span>
              <span className="dock-label">添加</span>
            </button>
            <ul tabIndex={0} className="dropdown-content menu bg-white rounded-box z-[9999] w-32 p-1 shadow-lg border border-slate-200 mb-2" style={{ opacity: 1, backgroundColor: 'white' }}>
              <li>
                <button
                  onClick={handleFoodRecord}
                  className="flex items-center justify-center gap-2 py-2 px-4 whitespace-nowrap"
                  disabled={isAIProcessing}
                >
                  <span className="text-base flex-shrink-0">🍽️</span>
                  <div className="font-medium text-sm text-center">食物记录</div>
                </button>
              </li>
              <li>
                <button
                  onClick={handleExerciseRecord}
                  className="flex items-center justify-center gap-2 py-2 px-4 whitespace-nowrap"
                  disabled={isAIProcessing}
                >
                  <span className="text-base flex-shrink-0">🏃‍♂️</span>
                  <div className="font-medium text-sm text-center">运动记录</div>
                </button>
              </li>
            </ul>
          </div>
        )}

        {/* 如果不显示添加按钮，显示占位符 */}
        {!showAddButton && (
          <button disabled aria-label="添加记录">
            <span className="text-lg opacity-30">➕</span>
            <span className="dock-label opacity-30">添加</span>
          </button>
        )}
        
        {/* 日历 */}
        <button
          onClick={() => navigate('/calendar')}
          className={`${isActive('/calendar') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="查看日历"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">📅</span>
          <span className="dock-label text-xs xl:text-sm">日历</span>
        </button>

        {/* 我的 */}
        <button
          onClick={() => navigate('/profile')}
          className={`${isActive('/profile') ? 'dock-active' : ''} hover:scale-105 transition-transform duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500`}
          aria-label="我的"
          tabIndex={0}
          disabled={isAIProcessing}
          style={isAIProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
        >
          <span className="text-lg xl:text-xl">👤</span>
          <span className="dock-label text-xs xl:text-sm">我的</span>
        </button>
      </div>

      {/* 食物记录模态窗 */}
      <UnifiedFoodRecordingModal
        isOpen={showFoodModal}
        onClose={() => setShowFoodModal(false)}
        onComplete={handleFoodRecordComplete}
      />

      {/* 运动记录模态窗 */}
      <ExerciseRecordingModal
        isOpen={showExerciseModal}
        onClose={() => setShowExerciseModal(false)}
        onComplete={handleExerciseRecordComplete}
      />
    </>
  );
};

export default BottomNavigation;
