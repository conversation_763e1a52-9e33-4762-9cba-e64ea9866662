import React, { useState, useEffect } from 'react';
import { useAIModelStore } from '@/domains/ai/stores/aiModelStore';
import { AIModelProvider, AIModelConfig } from '@/shared/types/aiModel';

// {{ AURA-X: Delete - 移除夏目提供商官方配置. Approval: 寸止(ID:**********). }}

interface AIModelManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AIModelManagementModal: React.FC<AIModelManagementModalProps> = ({
  isOpen,
  onClose,
}) => {
  const {
    models,
    activeModelId,
    addModel,
    updateModel,
    deleteModel,
    setActiveModel,
    validateModel,
    getModelList,
    // {{ AURA-X: Add - 使用新的缓存和验证状态管理. Approval: 寸止(ID:**********). }}
    getCachedModels,
    setCachedModels,
    getValidationState,
    setValidationState,
    clearValidationState,
    isConfigurationChanged
  } = useAIModelStore();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingModel, setEditingModel] = useState<AIModelConfig | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    provider: 'openai' as AIModelProvider,
    apiKey: '',
    baseUrl: '',
    modelName: '',
  });
  const [availableModels, setAvailableModels] = useState<Array<{id: string, supportsVision: boolean | null}>>([]);
  // {{ AURA-X: Modify - 扩展模型数据结构包含视觉支持信息. Approval: 寸止(ID:**********). }}

  // {{ AURA-X: Remove - 移除默认常用模型列表功能. Approval: 寸止(ID:**********). }}
  const [isValidating, setIsValidating] = useState(false);
  const [isGettingModels, setIsGettingModels] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [validationResult, setValidationResult] = useState<string>('');
  // {{ AURA-X: Add - 分离验证、获取模型和保存的状态. Approval: 寸止(ID:**********). }}
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordInput, setPasswordInput] = useState('');
  const [passwordType, setPasswordType] = useState<'add' | 'view'>('add');
  // {{ AURA-X: Add - 恢复密码验证状态变量. Approval: 寸止(ID:**********). }}
  const [showApiKey, setShowApiKey] = useState(false);

  // {{ AURA-X: Add - 输入框focus状态管理，用于控制清空按钮显示. Approval: 寸止(ID:**********). }}
  const [focusedInput, setFocusedInput] = useState<string | null>(null);

  // {{ AURA-X: Add - 模型名称输入模式切换状态. Approval: 寸止(ID:**********). }}
  const [isManualModelInput, setIsManualModelInput] = useState(false);

  // 处理模态窗关闭，清除验证状态
  const handleClose = () => {
    setValidationResult(''); // 清除验证结果
    setIsValidating(false);  // 重置验证状态
    setIsGettingModels(false); // 重置获取模型状态
    setIsSaving(false);      // 重置保存状态
    onClose();
    // {{ AURA-X: Add - 模态窗关闭时清除验证状态. Approval: 寸止(ID:**********). }}
  };

  // {{ AURA-X: Add - 组件初始化时加载缓存数据. Approval: 寸止(ID:**********). }}
  useEffect(() => {
    if (formData.apiKey && formData.baseUrl && formData.provider) {
      // 尝试加载缓存的模型数据
      const cachedModels = getCachedModels({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
      });

      if (cachedModels) {
        setAvailableModels(cachedModels);
      }

      // 检查验证状态
      if (formData.modelName) {
        const validationState = getValidationState({
          provider: formData.provider,
          apiKey: formData.apiKey,
          baseUrl: formData.baseUrl,
          modelName: formData.modelName,
        });

        if (validationState && validationState.isValidated) {
          setValidationResult(validationState.validationResult);
        }
      }
    }
  }, [formData.provider, formData.apiKey, formData.baseUrl, formData.modelName]); // {{ AURA-X: Optimize - 移除函数依赖项避免不必要重渲染. Approval: 寸止(ID:**********). }}

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'openai',
      apiKey: '',
      baseUrl: '',
      modelName: '',
    });
    setAvailableModels([]);
    setValidationResult('');
    setEditingModel(null);
    setShowApiKey(false);
    setShowPasswordModal(false);
    setPasswordInput('');
    // {{ AURA-X: Modify - 添加密码相关状态重置. Approval: 寸止(ID:**********). }}
  };

  // 密钥掩码函数
  const maskApiKey = (apiKey: string) => {
    if (!apiKey || apiKey.length < 8) return apiKey;
    const start = apiKey.substring(0, 4);
    const end = apiKey.substring(apiKey.length - 4);
    const middle = '*'.repeat(Math.min(apiKey.length - 8, 20));
    return `${start}${middle}${end}`;
  };

  // {{ AURA-X: Delete - 移除密码验证功能. Approval: 寸止(ID:**********). }}

  // {{ AURA-X: Modify - 支持多个官方模型配置. Approval: 寸止(ID:**********). }}
  const OFFICIAL_MODELS = [
    {
      name: '夏目官方模型1',
      provider: 'openai' as AIModelProvider,
      apiKey: 'sk-fCr04jsVUMdECSXyfSBYreh6da0ePzTwVmdctxSIQnGgfSxh',
      baseUrl: 'https://x666.me',
      modelName: 'gemini-2.5-pro',
      supportsVision: true,
      isActive: false,
    },
    {
      name: '夏目官方模型2',
      provider: 'openai' as AIModelProvider,
      apiKey: 'sk-YnVBTQs0OLXNk2aQFElOtFPOEqQLe0kaQEbivMvkuFFKqYpn',
      baseUrl: 'https://tbai.xin',
      modelName: 'gemini-2.5-pro',
      supportsVision: true,
      isActive: false,
    }
  ];
  // {{ AURA-X: Modify - 改回Gemini格式和正确的API端点. Approval: 寸止(ID:**********). }}

  // 密码验证
  const validatePassword = (password: string): boolean => {
    return password === 'xiamu';
  };

  // 处理官方模型选择
  const handleAddOfficialModel = () => {
    setPasswordType('add');
    setShowPasswordModal(true);
  };



  // 处理密码确认
  const handlePasswordConfirm = () => {
    if (validatePassword(passwordInput)) {
      setShowPasswordModal(false);
      setPasswordInput('');

      if (passwordType === 'add') {
        // 直接添加两个官方模型到列表
        OFFICIAL_MODELS.forEach((modelConfig) => {
          // 检查是否已存在该官方模型
          const existingModel = models.find(model =>
            model.name === modelConfig.name ||
            model.apiKey === modelConfig.apiKey
          );

          if (!existingModel) {
            addModel(modelConfig);
          }
        });

        // 设置第一个官方模型为活跃模型
        setTimeout(() => {
          const firstOfficialModel = models.find(model =>
            model.name === OFFICIAL_MODELS[0].name
          );
          if (firstOfficialModel) {
            setActiveModel(firstOfficialModel.id);
          }
        }, 100);

        setValidationResult('✅ 官方模型已添加到列表');
      } else if (passwordType === 'view') {
        // 查看API密钥
        setShowApiKey(true);
        setValidationResult('✅ 密码验证成功');
        setTimeout(() => setValidationResult(''), 2000);
      }
    } else {
      setValidationResult('❌ 密码错误，请重试');
      setTimeout(() => {
        setValidationResult('');
        setPasswordInput('');
      }, 2000);
    }
  };

  // {{ AURA-X: Modify - 统一视觉支持判断逻辑，与aiModelStore保持一致. Approval: 寸止(ID:**********). }}
  // 检查模型是否支持视觉功能
  const checkModelSupportsVision = (provider: string, modelName: string): boolean | null => {
    if (!modelName || modelName.trim() === '') {
      return null;
    }

    const lowerModelName = modelName.toLowerCase();

    switch (provider) {
      case 'openai':
        // OpenAI: 包含 'o' 或以 'gpt' 开头的都认为是视觉模型
        if (lowerModelName.startsWith('o') || lowerModelName.startsWith('gpt')||lowerModelName.includes('gemini')) {
          return true;
        }

        // {{ AURA-X: Modify - 简化状态，移除仅文本状态，统一为无法判断. Approval: 寸止(ID:**********). }}
        // 其他 OpenAI 模型无法判断
        return null;

      case 'gemini':
        // Gemini: 包含 'gemini' 的都支持视觉
        if (lowerModelName.includes('gemini')) {
          return true;
        }

        // 其他 Gemini 模型无法判断
        return null;

      default:
        // 第三方模型无法判断
        return null;
    }
  };
  // {{ AURA-X: Modify - 将Gemini检测改为前缀匹配. Approval: 寸止(ID:**********). }}
  // {{ AURA-X: Add - 基于官方文档的视觉模型检测函数. Approval: 寸止(ID:**********). }}

  // 获取详细错误信息和解决建议
  const getDetailedErrorMessage = (error: any): string => {
    if (error instanceof Error) {
      const message = error.message.toLowerCase();

      // 网络错误
      if (message.includes('network') || message.includes('fetch')) {
        return '网络连接失败，请检查网络连接或API端点地址';
      }

      // 认证错误
      if (message.includes('401') || message.includes('unauthorized') || message.includes('api key')) {
        return 'API密钥无效，请检查密钥是否正确或是否已过期';
      }

      // 权限错误
      if (message.includes('403') || message.includes('forbidden')) {
        return 'API密钥权限不足，请检查密钥是否有访问模型列表的权限';
      }

      // 服务器错误
      if (message.includes('500') || message.includes('502') || message.includes('503')) {
        return 'API服务器暂时不可用，请稍后重试';
      }

      // 超时错误
      if (message.includes('timeout')) {
        return '请求超时，请检查网络连接或稍后重试';
      }

      return error.message;
    }

    return '未知错误，请检查网络连接和API配置';
  };
  // {{ AURA-X: Add - 详细错误处理和用户建议. Approval: 寸止(ID:**********). }}

  // 处理API密钥可见性切换
  const handleToggleApiKeyVisibility = () => {
    // 检查是否是官方模型的API密钥
    const isOfficialModel = OFFICIAL_MODELS.some(model => model.apiKey === formData.apiKey);

    if (isOfficialModel && !showApiKey) {
      // 官方模型需要密码验证才能查看API密钥
      setPasswordType('view');
      setShowPasswordModal(true);
    } else {
      // 普通模型或已经显示的情况下直接切换
      setShowApiKey(!showApiKey);
    }
  };
  // {{ AURA-X: Add - 添加官方模型处理函数，使用新架构. Approval: 寸止(ID:**********). }}

  // 编辑模型
  const handleEditModel = (model: AIModelConfig) => {
    setEditingModel(model);
    setFormData({
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      baseUrl: model.baseUrl,
      modelName: model.modelName,
    });
    setShowAddForm(true);
  };

  // {{ AURA-X: Modify - 手动获取模型强制刷新，不使用缓存. Approval: 寸止(ID:**********). }}
  const handleGetModels = async (forceRefresh: boolean = true) => {
    if (!formData.apiKey || !formData.baseUrl) {
      setValidationResult('请先填写API Key和Base URL');
      return;
    }

    // 手动获取模型时强制刷新，清除缓存
    if (forceRefresh) {
      setValidationResult('🔄 正在重新获取最新模型列表...');
    } else {
      // 首先检查缓存（仅在非强制刷新时）
      const cachedModels = getCachedModels({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
      });

      if (cachedModels) {
        setAvailableModels(cachedModels);
        setValidationResult(`从缓存加载 ${cachedModels.length} 个模型`);
        return;
      }
    }

    setIsGettingModels(true);
    try {
      const result = await fetchAvailableModels({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
      });
      if (result.success) {
        const models = result.models || [];
        setAvailableModels(models);
        // 缓存获取的模型数据
        setCachedModels({
          provider: formData.provider,
          apiKey: formData.apiKey,
          baseUrl: formData.baseUrl,
        }, models);
        setValidationResult(`成功获取到 ${models.length} 个模型`);
      } else {
        // {{ AURA-X: Modify - 改进错误处理，显示具体错误信息. Approval: 寸止(ID:**********). }}
        const errorMessage = (result as any).error || '服务器返回了无效的响应格式';
        const detailedError = getDetailedErrorMessage(new Error(errorMessage));
        setValidationResult(`❌ 获取模型列表失败: ${detailedError}`);
      }
    } catch (error) {
      const errorMessage = getDetailedErrorMessage(error);
      setValidationResult(`❌ 获取模型列表失败: ${errorMessage}`);
    } finally {
      setIsGettingModels(false);
    }
  };
  // {{ AURA-X: Modify - 使用独立的获取模型状态. Approval: 寸止(ID:**********). }}

  // {{ AURA-X: Modify - 智能验证逻辑，支持验证状态跟踪. Approval: 寸止(ID:**********). }}
  const handleValidateModel = async () => {
    if (!formData.apiKey || !formData.baseUrl || !formData.modelName) {
      setValidationResult('❌ 请填写完整的配置信息');
      return;
    }

    setIsValidating(true);
    setValidationResult('🔄 正在验证配置...');

    try {
      // 发送真实的测试请求
      const testResult = await sendTestRequest({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
        modelName: formData.modelName,
      });

      if (testResult.success) {
        const successMessage = '✅ 配置验证成功！API响应正常';
        setValidationResult(successMessage);

        // 保存验证状态
        setValidationState({
          provider: formData.provider,
          apiKey: formData.apiKey,
          baseUrl: formData.baseUrl,
          modelName: formData.modelName,
        }, {
          isValidated: true,
          validationTimestamp: Date.now(),
          validationResult: successMessage,
        });
      } else {
        const errorMessage = `❌ 验证失败: API响应异常`;
        setValidationResult(errorMessage);

        // 清除验证状态
        clearValidationState({
          provider: formData.provider,
          apiKey: formData.apiKey,
          baseUrl: formData.baseUrl,
          modelName: formData.modelName,
        });
      }
    } catch (error) {
      const errorMessage = getDetailedErrorMessage(error);
      const fullErrorMessage = `❌ 验证失败: ${errorMessage}`;
      setValidationResult(fullErrorMessage);

      // 清除验证状态
      clearValidationState({
        provider: formData.provider,
        apiKey: formData.apiKey,
        baseUrl: formData.baseUrl,
        modelName: formData.modelName,
      });
    } finally {
      setIsValidating(false);
    }
  };

  // 发送测试请求
  const sendTestRequest = async (config: any) => {
    // 根据提供商选择认证方式和请求格式
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    let url = '';
    let testPayload: any;

    if (config.provider === 'openai') {
      // OpenAI API格式
      headers['Authorization'] = `Bearer ${config.apiKey}`;
      url = `${config.baseUrl}/v1/chat/completions`;
      testPayload = {
        model: config.modelName,
        messages: [{ role: "user", content: "Hello" }],
        max_tokens: 10
      };
    } else {
      // Gemini API格式，使用x-goog-api-key头部认证
      headers['x-goog-api-key'] = config.apiKey;
      url = `${config.baseUrl}/v1beta/models/${config.modelName}:generateContent`;
      testPayload = {
        contents: [{
          parts: [{
            text: "Hello"
          }]
        }]
      };
    }
    // {{ AURA-X: Modify - 使用官方x-goog-api-key认证. Approval: 寸止(ID:**********). }}
    // {{ AURA-X: Modify - 支持OpenAI和Gemini不同的请求格式. Approval: 寸止(ID:**********). }}

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(testPayload),
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    // 检查响应格式是否正确
    if (config.provider === 'openai') {
      // OpenAI响应格式验证
      if (!result.choices || !Array.isArray(result.choices)) {
        throw new Error('OpenAI API响应格式不正确');
      }
    } else {
      // Gemini响应格式验证
      if (!result.candidates || !Array.isArray(result.candidates)) {
        throw new Error('Gemini API响应格式不正确');
      }
    }
    // {{ AURA-X: Modify - 支持不同提供商的响应格式验证. Approval: 寸止(ID:**********). }}

    return { success: true, data: result };
  };

  // 获取可用模型列表 - 支持不同API格式
  const fetchAvailableModels = async (config: any) => {
    try {
      // 根据提供商选择认证方式和端点
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      let url = '';
      if (config.provider === 'openai') {
        // OpenAI API格式
        headers['Authorization'] = `Bearer ${config.apiKey}`;
        url = `${config.baseUrl}/v1/models`;
      } else {
        // Gemini API格式，使用x-goog-api-key头部认证
        headers['x-goog-api-key'] = config.apiKey;
        url = `${config.baseUrl}/v1beta/models`;
      }
      // {{ AURA-X: Modify - 使用官方x-goog-api-key认证. Approval: 寸止(ID:**********). }}
      // {{ AURA-X: Modify - 移除夏目提供商支持. Approval: 寸止(ID:**********). }}

      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        // {{ AURA-X: Modify - 改进错误处理，返回详细错误信息. Approval: 寸止(ID:**********). }}
        const errorText = await response.text();
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}${errorText ? ` - ${errorText}` : ''}`,
          models: []
        };
      }

    const result = await response.json();
    let models: Array<{id: string, supportsVision: boolean | null}> = [];
    // {{ AURA-X: Fix - 修复模型数组类型声明. Approval: 寸止(ID:**********). }}

    // 根据提供商解析不同的响应格式
    if (config.provider === 'openai') {
      // OpenAI API响应格式: { "data": [{ "id": "gpt-4", "object": "model", "features": [...] }] }
      models = result.data?.map((model: any) => {
        // 检查模型是否支持视觉功能（通过features字段）
        const hasVisionFeature = model.features?.some((feature: string) =>
          feature.toLowerCase().includes('vision') ||
          feature.toLowerCase().includes('image') ||
          feature.toLowerCase().includes('multimodal')
        ) || false;

        // 如果没有features字段，使用已知的视觉模型列表作为后备
        return {
          id: model.id,
          supportsVision: hasVisionFeature ? true : checkModelSupportsVision('openai', model.id)
        };
      }) || [];
    } else {
      // Gemini API响应格式: { "models": [{ "name": "models/gemini-pro", ... }] }
      models = result.models?.map((model: any) => {
        const name = model.name || model.id;
        const modelId = name?.replace('models/', '') || name;
        return {
          id: modelId,
          supportsVision: true // 所有Gemini对话模型都支持视觉
        };
      }) || [];
    }
    // {{ AURA-X: Modify - 解析OpenAI features字段判断视觉支持. Approval: 寸止(ID:1736938600). }}

      return { success: true, models };
    } catch (error) {
      // {{ AURA-X: Add - 添加异常处理，返回错误信息. Approval: 寸止(ID:**********). }}
      return {
        success: false,
        error: error instanceof Error ? error.message : '网络请求失败',
        models: []
      };
    }
  };

  // {{ AURA-X: Modify - 智能保存验证逻辑. Approval: 寸止(ID:**********). }}
  const handleSaveModel = async () => {
    if (!formData.name || !formData.apiKey || !formData.baseUrl || !formData.modelName) {
      setValidationResult('❌ 请填写完整的配置信息');
      return;
    }

    // {{ AURA-X: Modify - 简化保存逻辑，移除验证配置步骤. Approval: 寸止(ID:**********). }}
    setIsSaving(true);
    setValidationResult('🔄 正在保存模型配置...');
    try {
      const modelConfig = {
        ...formData,
        supportsVision: checkModelSupportsVision(formData.provider, formData.modelName),
        isActive: false,
      };

      // 多模态检测警告
      const isMultimodal = modelConfig.supportsVision;
      let successMessage = '✅ 模型保存成功！';

      if (!isMultimodal) {
        successMessage += '\n⚠️ 该模型不支持图片识别功能，图片识别可能无法正常工作';
      }

      if (editingModel) {
        updateModel(editingModel.id, modelConfig);
      } else {
        addModel(modelConfig);
      }

      setValidationResult(successMessage);
      // {{ AURA-X: Modify - 移除延迟，立即关闭模态窗. Approval: 寸止(ID:**********). }}
      setShowAddForm(false);
      resetForm();
    } catch (error) {
      setValidationResult('❌ 保存失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setIsSaving(false);
    }
  };

  // 默认配置
  const getDefaultBaseUrl = (provider: AIModelProvider) => {
    switch (provider) {
      case 'openai':
        return 'https://api.openai.com';
      case 'gemini':
        return 'https://generativelanguage.googleapis.com';
        // {{ AURA-X: Modify - 使用官方Gemini API端点. Approval: 寸止(ID:**********). }}
      // {{ AURA-X: Delete - 移除夏目提供商默认URL. Approval: 寸止(ID:**********). }}
      default:
        return '';
    }
  };

  // 当provider改变时更新baseUrl
  useEffect(() => {
    if (formData.provider) {
      setFormData(prev => ({
        ...prev,
        baseUrl: getDefaultBaseUrl(prev.provider),
      }));
    }
  }, [formData.provider]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-[60] flex items-center justify-center p-4 pb-20"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)',
        // {{ AURA-X: Modify - 参考FoodRecognitionModal添加pb-20确保居中显示. Approval: 寸止(ID:**********). }}
      }}
      onClick={handleClose}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md xl:max-w-4xl 2xl:max-w-5xl h-[calc(100vh-10rem)] sm:h-[calc(100vh-8rem)] xl:h-[calc(100vh-6rem)] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        // {{ AURA-X: Modify - 优化响应式高度计算，移动端预留更多空间. Approval: 寸止(ID:**********). }}
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)',
          minHeight: '400px', // 设置最小高度确保基本可用性
          maxHeight: 'calc(100vh - 8rem)' // 移动端预留更多空间
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 xl:p-8 pb-4 xl:pb-6">
          <div>
            <h2 className="text-xl xl:text-2xl font-bold text-gray-900 mb-1">AI模型管理</h2>
            <p className="text-sm xl:text-base text-gray-500">配置和管理AI模型</p>
          </div>
          <button
            onClick={handleClose}
            className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
            tabIndex={0}
          >
            <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-4 xl:p-6 pt-2 space-y-4 xl:space-y-6 pb-16 xl:pb-20 min-h-0">
          {/* {{ AURA-X: Modify - 添加min-h-0确保flex-1正确工作，实现真正的自适应高度. Approval: 寸止(ID:**********). }} */}
          {!showAddForm ? (
            // 模型列表视图
            <div>
              {/* 只在有模型时显示头部和添加按钮 */}
              {models.length > 0 && (
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">模型列表</h3>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAddOfficialModel()}
                      className="btn btn-primary btn-sm text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
                    >
                      官方模型
                    </button>
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="btn btn-success text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
                    >
                      + 自定义模型
                    </button>
                    {/* {{ AURA-X: Add - 恢复官方模型按钮，使用新架构. Approval: 寸止(ID:**********). }} */}
                  </div>
                </div>
              )}

              {models.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-6xl mb-6">🤖</div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">开始配置AI模型</h3>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    配置AI模型后，您就可以使用智能食物识别和营养分析功能了。
                    支持文本识别和图片识别两种方式。
                  </p>

                  {/* {{ AURA-X: Modify - 恢复官方模型和自定义模型选项. Approval: 寸止(ID:**********). }} */}
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-6">
                    <button
                      onClick={() => handleAddOfficialModel()}
                      className="btn btn-primary text-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] px-8"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      使用官方模型
                    </button>
                    <button
                      onClick={() => setShowAddForm(true)}
                      className="btn btn-outline rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px] px-8"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      自定义模型
                    </button>
                  </div>

                  <p className="text-sm text-gray-400">
                    推荐使用官方模型，配置简单且稳定可靠
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {/* {{ AURA-X: Modify - 优化模型卡片间距，减少垂直空间占用. Approval: 寸止(ID:1737098300). }} */}
                  {models.map((model) => (
                    <div
                      key={model.id}
                      className={`border rounded-xl p-3 ${
                        model.id === activeModelId ? 'border-emerald-500 bg-emerald-50' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          {/* {{ AURA-X: Modify - 进一步优化图标显示，更紧凑的设计. Approval: 寸止(ID:**********). }} */}
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-semibold text-gray-900">{model.name}</h4>
                            <div className="flex items-center gap-1">
                              {model.id === activeModelId && (
                                <span className="bg-emerald-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center" title="当前使用">
                                  <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </span>
                              )}
                              {model.supportsVision && (
                                <span className="bg-blue-500 text-white text-xs w-4 h-4 rounded-full flex items-center justify-center" title="支持图像识别">
                                  <svg className="w-2.5 h-2.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                                  </svg>
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="text-sm text-gray-600">
                            <p>模型: {model.modelName}</p>
                            {/* {{ AURA-X: Remove - 移除提供商信息显示，简化UI. Approval: 寸止(ID:1737098000). }} */}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {model.id !== activeModelId && (
                            <button
                              onClick={() => setActiveModel(model.id)}
                              className="btn btn-success btn-sm text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 min-h-[32px] w-8 h-8 p-0"
                              title="设为当前模型"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </button>
                          )}
                          <button
                            onClick={() => handleEditModel(model)}
                            className="btn btn-primary btn-sm text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 min-h-[32px] w-8 h-8 p-0"
                            title="编辑模型"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                          <button
                            onClick={() => deleteModel(model.id)}
                            className="btn btn-error btn-sm text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 min-h-[32px] w-8 h-8 p-0"
                            title="删除模型"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                        {/* {{ AURA-X: Replace - 将操作按钮改为图标显示. Approval: 寸止(ID:1736939100). }} */}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            // 添加/编辑表单视图
            <div>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingModel ? '编辑模型' : '添加新模型'}
                </h3>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    resetForm();
                  }}
                  className="btn btn-outline btn-sm rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200"
                >
                  返回列表
                </button>
              </div>

              <div className="space-y-6">
                {/* 基本信息 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服务商 <span className="text-red-500">*</span>
                    </label>
                    {/* {{ AURA-X: Add - 为必填字段添加红色标识. Approval: 寸止(ID:**********). }} */}
                    {/* {{ AURA-X: Modify - 将模型名称标签改为服务商. Approval: 寸止(ID:**********). }} */}
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        onFocus={() => setFocusedInput('name')}
                        onBlur={() => setFocusedInput(null)}
                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                        placeholder="输入名称"
                      />
                      {formData.name && focusedInput === 'name' && (
                        <button
                          type="button"
                          onMouseDown={(e) => {
                            e.preventDefault();
                            setFormData(prev => ({ ...prev, name: '' }));
                          }}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                          title="清空"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      服务商类型 <span className="text-red-500">*</span>
                    </label>
                    {/* {{ AURA-X: Modify - 将提供商标签改为服务商类型. Approval: 寸止(ID:**********). }} */}
                    <div className="dropdown dropdown-bottom w-full">
                      <div
                        tabIndex={0}
                        role="button"
                        className="btn btn-outline w-full justify-between rounded-lg border-gray-300 hover:border-emerald-500 focus:border-emerald-500"
                      >
                        <span>{formData.provider === 'openai' ? 'OpenAI' : formData.provider === 'gemini' ? 'Google Gemini' : '请选择提供商'}</span>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                      <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-gray-200">
                        <li>
                          <a onClick={(e) => {
                            setFormData(prev => ({ ...prev, provider: 'openai' }));
                            (e.target as HTMLElement).closest('.dropdown')?.removeAttribute('open');
                            (document.activeElement as HTMLElement)?.blur();
                          }} className={`flex items-center justify-between ${formData.provider === 'openai' ? 'bg-emerald-50 text-emerald-700' : ''}`}>
                            <span>OpenAI</span>
                            {/* {{ AURA-X: Add - 为选中的选项添加视觉标识. Approval: 寸止(ID:**********). }} */}
                            {formData.provider === 'openai' && (
                              <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </a>
                        </li>
                        <li>
                          <a onClick={(e) => {
                            setFormData(prev => ({ ...prev, provider: 'gemini' }));
                            (e.target as HTMLElement).closest('.dropdown')?.removeAttribute('open');
                            (document.activeElement as HTMLElement)?.blur();
                          }} className={`flex items-center justify-between ${formData.provider === 'gemini' ? 'bg-emerald-50 text-emerald-700' : ''}`}>
                            <span>Google Gemini</span>
                            {formData.provider === 'gemini' && (
                              <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            )}
                          </a>
                        </li>
                      </ul>
                    </div>
                    {/* {{ AURA-X: Replace - 使用DaisyUI dropdown替换原生select. Approval: 寸止(ID:**********). }} */}
                  </div>
                </div>

                {/* API配置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    API Key <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type={showApiKey ? "text" : "password"}
                      value={formData.apiKey}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                      onFocus={() => setFocusedInput('apiKey')}
                      onBlur={() => setFocusedInput(null)}
                      className="w-full px-3 py-2 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="输入API密钥"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 gap-1">
                      {formData.apiKey && focusedInput === 'apiKey' && (
                        <button
                          type="button"
                          onMouseDown={(e) => {
                            e.preventDefault();
                            setFormData(prev => ({ ...prev, apiKey: '' }));
                          }}
                          className="text-gray-400 hover:text-gray-600 p-1"
                          title="清空"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      )}
                      <button
                        type="button"
                        onClick={() => handleToggleApiKeyVisibility()}
                        className="text-gray-400 hover:text-gray-600 p-1"
                      >
                      {/* {{ AURA-X: Modify - 添加密码保护的API密钥查看. Approval: 寸止(ID:**********). }} */}
                      {showApiKey ? (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      )}
                    </button>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Base URL <span className="text-red-500">*</span>
                  </label>
                  <div className="relative">
                    <input
                      type="url"
                      value={formData.baseUrl}
                      onChange={(e) => setFormData(prev => ({ ...prev, baseUrl: e.target.value }))}
                      onFocus={() => setFocusedInput('baseUrl')}
                      onBlur={() => setFocusedInput(null)}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="API服务地址"
                    />
                    {formData.baseUrl && focusedInput === 'baseUrl' && (
                      <button
                        type="button"
                        onMouseDown={(e) => {
                          e.preventDefault();
                          setFormData(prev => ({ ...prev, baseUrl: '' }));
                        }}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                        title="清空"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    💡 提示：URL以"#"结尾时将直接使用您的地址
                  </p>
                  {/* {{ AURA-X: Add - 添加Base URL特殊处理的用户提示. Approval: 寸止(ID:1737098700). }} */}
                </div>

                {/* 模型选择 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <label className="block text-sm font-medium text-gray-700">
                        模型名称 <span className="text-red-500">*</span>
                      </label>
                      {/* {{ AURA-X: Add - 添加模型名称输入模式切换图标. Approval: 寸止(ID:**********). }} */}
                      <button
                        type="button"
                        onClick={() => setIsManualModelInput(!isManualModelInput)}
                        className="text-gray-400 hover:text-gray-600 p-1"
                        title={isManualModelInput ? "切换到下拉选择" : "切换到手动输入"}
                      >
                        {isManualModelInput ? (
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        ) : (
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        )}
                      </button>
                    </div>
                    {/* {{ AURA-X: Keep - 保持模型名称标签不变. Approval: 寸止(ID:**********). }} */}
                    <button
                      onClick={() => handleGetModels(true)} // {{ AURA-X: Fix - 手动获取强制刷新. Approval: 寸止(ID:**********). }}
                      disabled={isGettingModels}
                      className="text-sm text-emerald-600 hover:text-emerald-700 disabled:opacity-50"
                    >
                      {isGettingModels ? '获取中...' : '手动获取模型'}
                    </button>
                    {/* {{ AURA-X: Modify - 使用独立的获取模型状态. Approval: 寸止(ID:**********). }} */}
                  </div>
                  
                  <div className="relative">
                    {/* {{ AURA-X: Add - 根据切换状态显示不同的输入方式. Approval: 寸止(ID:**********). }} */}
                    {isManualModelInput ? (
                      // 手动输入模式
                      <div className="relative">
                        <input
                          type="text"
                          value={formData.modelName}
                          onChange={(e) => setFormData(prev => ({ ...prev, modelName: e.target.value }))}
                          onFocus={() => setFocusedInput('modelName')}
                          onBlur={() => setFocusedInput(null)}
                          className="w-full px-3 py-2 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                          placeholder="输入模型名称，如：gpt-4、gemini-pro"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 gap-1">
                          {/* {{ AURA-X: Add - 为手动输入的模型添加视觉支持检测. Approval: 寸止(ID:**********). }} */}
                          {formData.modelName && (
                            <span className="flex items-center">
                              {(() => {
                                const visionSupport = checkModelSupportsVision(formData.provider, formData.modelName);
                                if (visionSupport === true) {
                                  return (
                                    <div title="支持视觉">
                                      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                      </svg>
                                    </div>
                                  );
                                } else {
                                  // {{ AURA-X: Modify - 简化状态显示，移除仅文本状态. Approval: 寸止(ID:**********). }}
                                  return <span className="text-yellow-500" title="无法判断">❓</span>;
                                }
                              })()}
                            </span>
                          )}
                          {formData.modelName && focusedInput === 'modelName' && (
                            <button
                              type="button"
                              onMouseDown={(e) => {
                                e.preventDefault();
                                setFormData(prev => ({ ...prev, modelName: '' }));
                              }}
                              className="text-gray-400 hover:text-gray-600 p-1"
                              title="清空"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    ) : (
                      // 下拉选择模式
                      availableModels.length > 0 ? (
                      <div className="dropdown dropdown-bottom w-full">
                        <div
                          tabIndex={0}
                          role="button"
                          className="btn btn-outline w-full justify-between rounded-lg border-gray-300 hover:border-emerald-500 focus:border-emerald-500"
                        >
                          <span className="flex items-center gap-2">
                            {formData.modelName || '请选择模型'}
                            {formData.modelName && availableModels.find(m => m.id === formData.modelName) && (
                              <span className="flex items-center">
                                {availableModels.find(m => m.id === formData.modelName)?.supportsVision === true && (
                                  <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                  </svg>
                                )}
                                {availableModels.find(m => m.id === formData.modelName)?.supportsVision !== true && (
                                  <span className="text-yellow-500">❓</span>
                                )}
                              </span>
                            )}
                          </span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                        <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-gray-200 max-h-60 overflow-y-auto">
                          {availableModels.length > 0 ? availableModels.map(model => (
                            <li key={model.id}>
                              <a
                                onClick={(e) => {
                                  setFormData(prev => ({ ...prev, modelName: model.id }));
                                  (e.target as HTMLElement).closest('.dropdown')?.removeAttribute('open');
                                  (document.activeElement as HTMLElement)?.blur();
                                }}
                                className={`flex items-center justify-between ${formData.modelName === model.id ? 'bg-emerald-50 text-emerald-700' : ''}`}
                              >
                                <span className="flex items-center gap-2">
                                  {model.id}
                                  {/* {{ AURA-X: Add - 为选中的模型添加勾选标识. Approval: 寸止(ID:**********). }} */}
                                  {formData.modelName === model.id && (
                                    <svg className="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                  )}
                                </span>
                                <span className="flex items-center gap-1">
                                  {model.supportsVision === true && (
                                    <div title="支持视觉">
                                      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                      </svg>
                                    </div>
                                  )}
                                  {model.supportsVision !== true && (
                                    <span className="text-yellow-500" title="无法判断">❓</span>
                                  )}
                                </span>
                              </a>
                            </li>
                          )) : (
                            <li>
                              <span className="text-gray-500">暂无模型，请手动获取</span>
                            </li>
                          )}
                        </ul>
                      </div>
                    ) : (
                      <div className="dropdown dropdown-bottom w-full">
                        <div
                          tabIndex={0}
                          role="button"
                          className="btn btn-outline w-full justify-between rounded-lg border-gray-300 hover:border-emerald-500 focus:border-emerald-500"
                        >
                          <span>{formData.modelName || '暂无模型，请手动获取'}</span>
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                        <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-full p-2 shadow-lg border border-gray-200">
                          <li>
                            <span className="text-gray-500">暂无模型，请手动获取</span>
                          </li>
                        </ul>
                      </div>
                    ))}
                  </div>
                  {/* {{ AURA-X: Remove - 移除独立的手动输入框. Approval: 寸止(ID:**********). }} */}
                  {/* {{ AURA-X: Replace - 优化模型选择，支持默认模型和手动输入. Approval: 寸止(ID:**********). }} */}
                  {/* {{ AURA-X: Replace - 使用DaisyUI dropdown替换datalist，添加视觉标识. Approval: 寸止(ID:**********). }} */}
                  {availableModels.length > 0 && (
                    <p className="text-xs text-gray-500 mt-1">
                      已获取 {availableModels.length} 个可用模型，可直接选择
                    </p>
                  )}
                  {/* {{ AURA-X: Replace - 简化提示文本，只显示获取的模型数量. Approval: 寸止(ID:**********). }} */}
                </div>

                {/* 验证结果 */}
                {validationResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    validationResult.includes('✅') ? 'bg-green-50 text-green-800' :
                    validationResult.includes('❌') ? 'bg-red-50 text-red-800' :
                    'bg-blue-50 text-blue-800'
                  }`}>
                    <div className="whitespace-pre-line">
                      {validationResult}
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
                  <button
                    onClick={handleValidateModel}
                    disabled={isValidating}
                    className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50"
                  >
                    {isValidating ? '验证中...' : '验证配置'}
                  </button>
                  <button
                    onClick={handleSaveModel}
                    disabled={isSaving || isValidating}
                    className="btn btn-success text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] disabled:opacity-50"
                  >
                    {isSaving ? '保存中...' : '保存模型'}
                  </button>
                  {/* {{ AURA-X: Modify - 使用独立的保存状态显示. Approval: 寸止(ID:**********). }} */}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>



      {/* 密码确认模态窗 */}
      {showPasswordModal && (
        <div
          className="fixed inset-0 z-[70] flex items-center justify-center p-4"
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            backdropFilter: 'blur(8px)',
          }}
          onClick={(e) => {
            e.stopPropagation();
            setShowPasswordModal(false);
            setPasswordInput('');
          }}
        >
          <div
            className="relative bg-white rounded-2xl w-full max-w-sm shadow-2xl border border-gray-100 flex flex-col"
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 头部 */}
            <div className="flex items-center justify-between p-6 pb-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {passwordType === 'add' ? '官方模型验证' : 'API密钥查看验证'}
                </h3>
                <p className="text-sm text-gray-500">
                  {passwordType === 'add'
                    ? '请输入密码以使用官方模型配置'
                    : '请输入密码以查看API密钥'
                  }
                </p>
              </div>
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPasswordInput('');
                }}
                className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                tabIndex={0}
              >
                <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 内容 */}
            <div className="flex-1 p-6 pt-2 pb-20">
              <input
                type="password"
                placeholder="请输入密码"
                value={passwordInput}
                onChange={(e) => setPasswordInput(e.target.value)}
                className="input input-bordered w-full rounded-xl"
                onKeyDown={(e) => e.key === 'Enter' && handlePasswordConfirm()}
                autoFocus
              />
            </div>

            {/* 底部按钮 */}
            <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
              <button
                onClick={handlePasswordConfirm}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px]"
              >
                确认
              </button>
              <button
                onClick={() => {
                  setShowPasswordModal(false);
                  setPasswordInput('');
                }}
                className="btn btn-outline flex-1 rounded-xl border-gray-300 hover:bg-gray-100 transition-all duration-200 min-h-[44px]"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
      {/* {{ AURA-X: Add - 恢复密码确认模态窗. Approval: 寸止(ID:**********). }} */}


    </div>
  );
};

export default AIModelManagementModal;
