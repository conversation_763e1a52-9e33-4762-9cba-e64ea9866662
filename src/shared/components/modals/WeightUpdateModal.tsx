import React, { useState } from 'react';
import { useUserStore } from '@/domains/user/stores/userStore';
import { WeightUpdateForm } from '@/shared/types';

interface WeightUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentWeight: number;
}

const WeightUpdateModal: React.FC<WeightUpdateModalProps> = ({
  isOpen,
  onClose,
  currentWeight
}) => {
  const [formData, setFormData] = useState<WeightUpdateForm>({
    weight: currentWeight,
    note: '',
    date: new Date()
  });
  const [isLoading, setIsLoading] = useState(false);
  const { updateWeight, error } = useUserStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.weight <= 0 || formData.weight > 500) {
      alert('请输入有效的体重（0-500kg）');
      return;
    }

    setIsLoading(true);
    try {
      await updateWeight(formData);
      onClose();
    } catch (error) {
      console.error('更新体重失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        weight: currentWeight,
        note: '',
        date: new Date()
      });
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-slate-800">更新体重</h3>
            <button
              onClick={handleClose}
              disabled={isLoading}
              className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors disabled:opacity-50"
            >
              <span className="text-gray-600">✕</span>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 体重输入 */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                当前体重 (kg)
              </label>
              <input
                type="number"
                step="0.1"
                min="20"
                max="500"
                value={formData.weight}
                onChange={(e) => setFormData(prev => ({ ...prev, weight: parseFloat(e.target.value) || 0 }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                placeholder="请输入您的当前体重"
                required
              />
            </div>

            {/* 日期选择 */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                记录日期
              </label>
              <input
                type="date"
                value={formData.date?.toISOString().split('T')[0] || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, date: new Date(e.target.value) }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                required
              />
            </div>

            {/* 备注 */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                备注 (可选)
              </label>
              <textarea
                value={formData.note}
                onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors resize-none"
                rows={3}
                placeholder="记录体重变化的原因或感受..."
              />
            </div>

            {/* 体重变化提示 */}
            {formData.weight !== currentWeight && (
              <div className={`p-3 rounded-lg ${
                formData.weight > currentWeight 
                  ? 'bg-red-50 border border-red-200' 
                  : 'bg-green-50 border border-green-200'
              }`}>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${
                    formData.weight > currentWeight ? 'text-red-700' : 'text-green-700'
                  }`}>
                    {formData.weight > currentWeight ? '↑' : '↓'} 
                    体重变化: {Math.abs(formData.weight - currentWeight).toFixed(1)}kg
                  </span>
                </div>
              </div>
            )}

            {/* 错误提示 */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-3 pt-2">
              <button
                type="button"
                onClick={handleClose}
                disabled={isLoading}
                className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading || formData.weight <= 0}
                className="flex-1 px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>更新中...</span>
                  </div>
                ) : (
                  '更新体重'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default WeightUpdateModal;