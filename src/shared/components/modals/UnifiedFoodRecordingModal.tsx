/**
 * 统一食物记录模态窗
 * 整合文字和视觉识别功能，支持智能模式切换
 */

import React, { useState, useEffect, useRef } from 'react';
import { MealType } from '../../../domains/nutrition/types/nutrition';
import { useUnifiedFoodRecording, UnifiedFoodRecordingOptions } from '../../hooks/useUnifiedFoodRecording';
import { RecognitionMethod } from '../../types/recognition';

interface UnifiedFoodRecordingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (result: any) => void;
  defaultMeal?: MealType;
}

export function UnifiedFoodRecordingModal({
  isOpen,
  onClose,
  onComplete,
  defaultMeal = 'breakfast'
}: UnifiedFoodRecordingModalProps) {
  const [selectedMeal, setSelectedMeal] = useState<MealType>(defaultMeal);
  const [description, setDescription] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [recognitionMethod, setRecognitionMethod] = useState<RecognitionMethod>('auto');
  const [showCamera, setShowCamera] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const {
    startRecording,
    reset,
    isProcessing,
    error,
    result,
    progress,
    processingStep,
    validateInput,
    determineRecognitionMethod
  } = useUnifiedFoodRecording();

  // 重置状态当模态窗打开时
  useEffect(() => {
    if (isOpen) {
      reset();
      setDescription('');
      setSelectedImages([]);
      setRecognitionMethod('auto');
      setShowCamera(false);
    }
  }, [isOpen, reset]);

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedImages(prev => [...prev, ...files].slice(0, 5)); // 最多5张图片
  };

  // 移除图片
  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  // 开始摄像头
  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setShowCamera(true);
      }
    } catch (error) {
      console.error('摄像头启动失败:', error);
      alert('摄像头启动失败，请检查权限设置');
    }
  };

  // 拍照
  const takePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current;
      const video = videoRef.current;
      
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `photo-${Date.now()}.jpg`, { type: 'image/jpeg' });
            setSelectedImages(prev => [...prev, file].slice(0, 5));
          }
        }, 'image/jpeg', 0.8);
      }
    }
  };

  // 停止摄像头
  const stopCamera = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setShowCamera(false);
  };

  // 处理识别方法变化
  const handleMethodChange = (method: RecognitionMethod) => {
    setRecognitionMethod(method);
    
    // 清理不相关的输入
    if (method === 'text') {
      setSelectedImages([]);
      stopCamera();
    } else if (method === 'image') {
      // 图像模式下保留描述作为补充信息
    }
  };

  // 开始识别
  const handleStartRecognition = async () => {
    const options: UnifiedFoodRecordingOptions = {
      meal: selectedMeal,
      description,
      images: selectedImages,
      method: recognitionMethod
    };

    // 验证输入
    const validation = validateInput(options);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    try {
      const result = await startRecording(options);
      onComplete(result);
    } catch (error) {
      console.error('食物记录失败:', error);
    }
  };

  // 获取当前识别方法
  const currentMethod = determineRecognitionMethod({
    meal: selectedMeal,
    description,
    images: selectedImages,
    method: recognitionMethod
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 pb-20">
      <div 
        className="absolute inset-0 bg-black/40 backdrop-blur-sm"
        onClick={onClose}
      />
      
      <div className="relative bg-white rounded-2xl w-full max-w-md max-h-[85vh] overflow-hidden shadow-2xl">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 pb-4 border-b border-gray-100">
          <div>
            <h2 className="text-xl font-bold text-gray-900">食物记录</h2>
            <p className="text-sm text-gray-500 mt-1">
              {currentMethod === 'image' ? '图片识别' : 
               currentMethod === 'text' ? '文字分析' : '智能识别'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* 餐次选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">餐次</label>
            <div className="grid grid-cols-2 gap-2">
              {[
                { value: 'breakfast', label: '早餐' },
                { value: 'lunch', label: '午餐' },
                { value: 'dinner', label: '晚餐' },
                { value: 'snack', label: '加餐' }
              ].map(meal => (
                <button
                  key={meal.value}
                  onClick={() => setSelectedMeal(meal.value as MealType)}
                  className={`p-3 rounded-xl border-2 transition-all ${
                    selectedMeal === meal.value
                      ? 'border-emerald-500 bg-emerald-50 text-emerald-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {meal.label}
                </button>
              ))}
            </div>
          </div>

          {/* 识别方法选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">识别方式</label>
            <div className="flex gap-2">
              {[
                { value: 'auto', label: '智能', icon: '🤖' },
                { value: 'text', label: '文字', icon: '📝' },
                { value: 'image', label: '图片', icon: '📷' }
              ].map(method => (
                <button
                  key={method.value}
                  onClick={() => handleMethodChange(method.value as RecognitionMethod)}
                  className={`flex-1 p-3 rounded-xl border-2 transition-all ${
                    recognitionMethod === method.value
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-lg mb-1">{method.icon}</div>
                  <div className="text-sm font-medium">{method.label}</div>
                </button>
              ))}
            </div>
          </div>

          {/* 描述输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述 {currentMethod === 'text' && <span className="text-red-500">*</span>}
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={
                currentMethod === 'image' 
                  ? '可选：补充说明图片中的食物...'
                  : '请描述您吃的食物，包括种类、数量等...'
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 resize-none"
              rows={3}
            />
          </div>

          {/* 图片上传区域 */}
          {(recognitionMethod === 'image' || recognitionMethod === 'auto') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                图片 {currentMethod === 'image' && <span className="text-red-500">*</span>}
              </label>
              
              {/* 图片预览 */}
              {selectedImages.length > 0 && (
                <div className="grid grid-cols-3 gap-2 mb-3">
                  {selectedImages.map((file, index) => (
                    <div key={index} className="relative">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`预览 ${index + 1}`}
                        className="w-full h-20 object-cover rounded-lg"
                      />
                      <button
                        onClick={() => removeImage(index)}
                        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* 上传按钮 */}
              <div className="flex gap-2">
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="flex-1 p-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">📁</div>
                    <div className="text-sm text-gray-600">选择图片</div>
                  </div>
                </button>
                
                <button
                  onClick={showCamera ? stopCamera : startCamera}
                  className="flex-1 p-3 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">{showCamera ? '❌' : '📷'}</div>
                    <div className="text-sm text-gray-600">
                      {showCamera ? '关闭相机' : '拍照'}
                    </div>
                  </div>
                </button>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>
          )}

          {/* 摄像头界面 */}
          {showCamera && (
            <div className="space-y-3">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                className="w-full rounded-lg"
              />
              <button
                onClick={takePhoto}
                className="w-full p-3 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors"
              >
                拍照
              </button>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          {/* 处理进度 */}
          {isProcessing && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">{processingStep}</span>
                <span className="text-gray-600">{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-emerald-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="p-6 pt-3 border-t border-gray-100">
          <div className="flex gap-3">
            <button
              onClick={onClose}
              disabled={isProcessing}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              取消
            </button>
            <button
              onClick={handleStartRecognition}
              disabled={isProcessing}
              className="flex-1 px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors disabled:opacity-50"
            >
              {isProcessing ? '识别中...' : '开始识别'}
            </button>
          </div>
        </div>

        {/* 隐藏的canvas用于拍照 */}
        <canvas ref={canvasRef} className="hidden" />
      </div>
    </div>
  );
}
