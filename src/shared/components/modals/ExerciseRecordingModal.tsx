import React, { useState, useRef } from 'react';
import { useExerciseRecording } from '../../hooks/useExerciseRecording';

export type RecognitionMethod = 'text' | 'image' | 'auto';

interface ExerciseRecordingModalProps {
  isOpen: boolean;
  onClose: () => void;
  method: RecognitionMethod;
  onRecognitionComplete?: (result: any) => void;
  currentDate?: Date;
  onProcessingStateChange?: (isProcessing: boolean) => void;
}

const ExerciseRecordingModal: React.FC<ExerciseRecordingModalProps> = ({
  isOpen,
  onClose,
  method,
  onRecognitionComplete,
  currentDate,
  onProcessingStateChange
}) => {
  const [textInput, setTextInput] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [recognitionResult, setRecognitionResult] = useState<any | null>(null);
  const [showResultsView, setShowResultsView] = useState(false);
  const [enlargedImageIndex, setEnlargedImageIndex] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate || new Date());
  const [currentMethod, setCurrentMethod] = useState<'text' | 'image'>('text');

  // 智能检测识别方式
  const detectRecognitionMethod = (): 'text' | 'image' => {
    if (selectedImages.length > 0) {
      return 'image';
    }
    if (textInput.trim()) {
      return 'text';
    }
    return 'text';
  };

  // 自动更新识别方式
  React.useEffect(() => {
    if (method === 'auto') {
      const detectedMethod = detectRecognitionMethod();
      setCurrentMethod(detectedMethod);
    } else {
      setCurrentMethod(method);
    }
  }, [method, textInput, selectedImages]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const { state, startRecognition, stopRecognition } = useExerciseRecording();

  // 重置状态（完全清理所有状态）
  const resetState = () => {
    setTextInput('');
    setSelectedImages([]);
    setImagePreviews([]);
    setRecognitionResult(null);
    setShowResultsView(false);
    setEnlargedImageIndex(null);

    // 清理文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = '';
    }

    // 停止任何正在进行的识别
    if (state.isProcessing) {
      stopRecognition();
    }
  };

  // 处理弹窗关闭（确保完全清理状态）
  const handleClose = () => {
    if (!state.isProcessing) {
      resetState();
      onProcessingStateChange?.(false);
      setTimeout(() => {
        onClose();
      }, 0);
    }
  };

  // 处理图片选择（支持多张）
  const handleImageSelect = (file: File) => {
    if (selectedImages.length >= 5) {
      alert('最多只能上传5张图片');
      return;
    }

    if (!file || !(file instanceof File)) {
      console.error('Invalid file object:', file);
      return;
    }

    const newImages = [...selectedImages, file];
    setSelectedImages(newImages);

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === 'string') {
        const newPreviews = [...imagePreviews, result];
        setImagePreviews(newPreviews);
      }
    };
    reader.onerror = (e) => {
      console.error('FileReader error:', e);
      alert('图片读取失败，请重试');
    };
    reader.readAsDataURL(file);
  };

  // 删除单张图片
  const handleRemoveImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setSelectedImages(newImages);
    setImagePreviews(newPreviews);
  };

  // 处理相机拍照
  const handleCameraCapture = () => {
    cameraInputRef.current?.click();
  };

  // 处理相机拍照完成
  const handleCameraPhotoCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !(file instanceof File)) {
      console.error('Invalid camera file:', file);
      return;
    }

    handleImageSelect(file);

    if (event.target) {
      event.target.value = '';
    }
  };

  // 处理从相册选择文件
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 开始识别
  const handleStartRecognition = async () => {
    onProcessingStateChange?.(true);

    try {
      const combinedTextInput = textInput;
      const imagesToUse = selectedImages.length > 0 ? selectedImages : null;

      await startRecognition(
        currentMethod,
        combinedTextInput,
        imagesToUse,
        (result) => {
          setRecognitionResult(result);
          setShowResultsView(true);
          onProcessingStateChange?.(false);
        }
      );
    } catch (error) {
      console.error('识别失败:', error);
      onProcessingStateChange?.(false);
    }
  };

  // 处理最终提交
  const handleFinalSubmit = () => {
    if (!recognitionResult) {
      console.error('recognitionResult 为空，无法提交');
      alert('识别结果丢失，请重新识别');
      return;
    }

    try {
      const finalResult = {
        ...recognitionResult,
        selectedDate: selectedDate,
        additionalContext: textInput.trim() || undefined
      };

      if (typeof onRecognitionComplete === 'function') {
        onProcessingStateChange?.(false);
        onRecognitionComplete(finalResult);
        setTimeout(() => {
          handleClose();
        }, 100);
      } else {
        console.error('onRecognitionComplete 不是一个函数');
        onProcessingStateChange?.(false);
        alert('提交失败，请重试');
      }
    } catch (error) {
      console.error('提交过程中发生错误:', error);
      onProcessingStateChange?.(false);
      alert('提交失败，请重试');
    }
  };

  // 重新识别
  const handleReRecognize = () => {
    setRecognitionResult(null);
    setShowResultsView(false);
  };

  // 终止识别
  const handleStopRecognition = () => {
    if (state.isProcessing) {
      stopRecognition();
    }
    onProcessingStateChange?.(false);
  };

  // 检查是否可以开始识别
  const canStartRecognition = () => {
    return textInput.trim().length > 0 || selectedImages.length > 0;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4 pb-20"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)',
        willChange: 'auto',
        transform: 'translateZ(0)',
        ...(state.isProcessing && { touchAction: 'none' })
      }}
      onClick={handleClose}
      onTouchMove={state.isProcessing ? (e) => e.preventDefault() : undefined}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)',
          backfaceVisibility: 'hidden',
          perspective: '1000px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              运动记录
            </h2>
            <p className="text-sm text-gray-500">
              {currentMethod === 'image' ? '智能图片识别' : '智能文字分析'}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
            disabled={state.isProcessing}
            style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
            tabIndex={0}
          >
            <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-16">
          {!showResultsView ? (
            <>
              {/* 日期选择器 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">选择记录日期</h3>
                <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
                  <input
                    type="date"
                    value={selectedDate.toISOString().split('T')[0]}
                    onChange={(e) => setSelectedDate(new Date(e.target.value))}
                    max={new Date().toISOString().split('T')[0]}
                    className="input input-bordered input-sm w-full bg-white"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    当前选择：{selectedDate.toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      weekday: 'long'
                    })}
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* 识别结果视图 */
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800">识别结果</h3>
              </div>

              {recognitionResult && (
                <div className="space-y-3">
                  <div className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{recognitionResult.exerciseName || '运动项目'}</h4>
                        <div className="text-sm text-gray-600 mt-1">
                          <span className="font-medium">时长：</span>
                          {recognitionResult.duration || '未知'} 分钟
                        </div>
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">消耗卡路里：</span>
                          {recognitionResult.calories || 0} kcal
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 内容输入区域 - 只在非结果视图时显示 */}
          {!showResultsView && (
            <>
              {/* 文字描述输入 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">
                  描述运动
                </h3>
                <textarea
                  value={textInput}
                  onChange={(e) => setTextInput(e.target.value)}
                  placeholder={
                    selectedImages.length > 0
                      ? "可选：补充说明图片中的运动内容..."
                      : "例如：跑步30分钟，游泳1小时，举重训练..."
                  }
                  className="textarea textarea-bordered w-full h-32 resize-none"
                  disabled={state.isProcessing}
                  style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                />
                <div className="text-xs text-slate-500 mt-2">
                  💡 提示：描述越详细，AI识别越准确
                </div>
              </div>

              {/* 图片上传区域 */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-base font-medium text-slate-800 mb-2">
                    图片（可选） ({selectedImages.length}/5)
                  </h3>

                  {/* 图片预览网格 */}
                  {imagePreviews.length > 0 && (
                    <div className="grid grid-cols-3 gap-2 mb-3">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative">
                          <img
                            src={preview}
                            alt={`预览 ${index + 1}`}
                            className="w-full h-20 object-cover rounded-lg border border-slate-200 cursor-pointer hover:opacity-80 transition-opacity"
                            onClick={() => setEnlargedImageIndex(index)}
                          />
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveImage(index);
                            }}
                            className="absolute -top-1 -right-1 btn btn-ghost btn-xs btn-circle bg-red-500 text-white hover:bg-red-600"
                            disabled={state.isProcessing}
                          >
                            ✕
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 添加图片按钮 */}
                  {selectedImages.length < 5 && (
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        onClick={handleCameraCapture}
                        className="btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap"
                        disabled={state.isProcessing}
                        style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                      >
                        <span className="text-2xl flex-shrink-0">📷</span>
                        <span className="text-sm">拍照识别</span>
                      </button>
                      <button
                        onClick={handleFileUpload}
                        className="btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap"
                        disabled={state.isProcessing}
                        style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                      >
                        <span className="text-2xl flex-shrink-0">📁</span>
                        <span className="text-sm">从相册选择</span>
                      </button>
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file && file instanceof File) {
                        handleImageSelect(file);
                      } else if (file) {
                        console.error('Invalid file type from input:', file);
                      }
                    }}
                    className="hidden"
                  />
                </div>
              </div>
            </>
          )}

          {/* 错误提示 */}
          {state.error && (
            <div className="alert alert-error">
              <span className="text-white">{state.error}</span>
            </div>
          )}

          {/* 进度提示 */}
          {state.isProcessing && state.processingStep && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <div className="loading loading-spinner loading-sm text-blue-600"></div>
                <span className="text-sm text-blue-700 font-medium">{state.processingStep}</span>
              </div>
            </div>
          )}

          {/* 底部间距保障 */}
          <div className="h-4"></div>
        </div>

        {/* 操作按钮 */}
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {!state.isProcessing ? (
            showResultsView ? (
              /* 结果视图按钮 */
              <>
                <button
                  onClick={handleReRecognize}
                  className="btn btn-outline flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                >
                  重新识别
                </button>
                <button
                  onClick={handleFinalSubmit}
                  className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                >
                  <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>确认添加</span>
                </button>
              </>
            ) : (
              /* 初始识别按钮 */
              <button
                onClick={handleStartRecognition}
                disabled={!canStartRecognition() || state.isProcessing}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
              >
                <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span>开始识别</span>
              </button>
            )
          ) : (
            <button
              onClick={handleStopRecognition}
              className="btn btn-error text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
            >
              <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span>终止识别</span>
            </button>
          )}
        </div>

        {/* 隐藏的相机输入 */}
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraPhotoCapture}
          className="hidden"
        />

        {/* 图片放大查看模态框 */}
        {enlargedImageIndex !== null && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 pb-20" onClick={() => setEnlargedImageIndex(null)}>
            <div className="relative max-w-4xl max-h-4xl p-4">
              <img
                src={imagePreviews[enlargedImageIndex]}
                alt={`放大预览 ${enlargedImageIndex + 1}`}
                className="max-w-full max-h-full object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />

              {/* 关闭按钮 */}
              <button
                onClick={() => setEnlargedImageIndex(null)}
                className="absolute top-2 right-2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200"
              >
                ✕
              </button>

              {/* 图片导航 */}
              {imagePreviews.length > 1 && (
                <>
                  {enlargedImageIndex > 0 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEnlargedImageIndex(enlargedImageIndex - 1);
                      }}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200"
                    >
                      ←
                    </button>
                  )}
                  {enlargedImageIndex < imagePreviews.length - 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEnlargedImageIndex(enlargedImageIndex + 1);
                      }}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200"
                    >
                      →
                    </button>
                  )}
                </>
              )}

              {/* 图片计数 */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                {enlargedImageIndex + 1} / {imagePreviews.length}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExerciseRecordingModal;

