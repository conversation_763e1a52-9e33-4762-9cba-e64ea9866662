import React, { useState, useRef } from 'react';
import { useExerciseRecording } from '../../hooks/useExerciseRecording';

export type RecognitionMethod = 'text' | 'image' | 'auto';

interface ExerciseRecordingModalProps {
  isOpen: boolean;
  onClose: () => void;
  method: RecognitionMethod;
  onRecognitionComplete?: (result: any) => void;
  currentDate?: Date;
  onProcessingStateChange?: (isProcessing: boolean) => void;
}

const ExerciseRecordingModal: React.FC<ExerciseRecordingModalProps> = ({
  isOpen,
  onClose,
  method,
  onRecognitionComplete,
  currentDate,
  onProcessingStateChange
}) => {
  const [textInput, setTextInput] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [recognitionResult, setRecognitionResult] = useState<any | null>(null);
  const [showResultsView, setShowResultsView] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate || new Date());
  const [currentMethod, setCurrentMethod] = useState<'text' | 'image'>('text');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const { state, startRecording, reset } = useExerciseRecording();

  // 智能检测识别方式
  const detectRecognitionMethod = (): 'text' | 'image' => {
    if (selectedImages.length > 0) {
      return 'image';
    }
    if (textInput.trim()) {
      return 'text';
    }
    return 'text';
  };

  // 自动更新识别方式
  React.useEffect(() => {
    if (method === 'auto') {
      const detectedMethod = detectRecognitionMethod();
      setCurrentMethod(detectedMethod);
    } else {
      setCurrentMethod(method);
    }
  }, [method, textInput, selectedImages]);

  // 重置状态
  const resetState = () => {
    setTextInput('');
    setSelectedImages([]);
    setImagePreviews([]);
    setRecognitionResult(null);
    setShowResultsView(false);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = '';
    }

    if (state.isProcessing) {
      reset();
    }
  };

  // 处理弹窗关闭
  const handleClose = () => {
    if (!state.isProcessing) {
      resetState();
      onProcessingStateChange?.(false);
      setTimeout(() => {
        onClose();
      }, 0);
    }
  };

  // 开始识别
  const handleStartRecognition = async () => {
    onProcessingStateChange?.(true);

    try {
      const combinedTextInput = textInput;
      const imagesToUse = selectedImages.length > 0 ? selectedImages : undefined;

      const result = await startRecording({
        description: combinedTextInput,
        images: imagesToUse,
        method: currentMethod
      });
      
      setRecognitionResult(result);
      setShowResultsView(true);
      onProcessingStateChange?.(false);
    } catch (error) {
      console.error('识别失败:', error);
      onProcessingStateChange?.(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 p-4 pb-20">
      <div className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col">
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">运动记录</h2>
            <p className="text-sm text-gray-500">
              {currentMethod === 'image' ? '智能图片识别' : '智能文字分析'}
            </p>
          </div>
          <button onClick={handleClose} className="btn btn-error text-white btn-sm">
            ✕
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-16">
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">描述运动</h3>
            <textarea
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="例如：跑步30分钟，游泳1小时，举重训练..."
              className="textarea textarea-bordered w-full h-32 resize-none"
            />
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          <button
            onClick={handleStartRecognition}
            disabled={!textInput.trim()}
            className="btn btn-primary text-white flex-1 rounded-xl"
          >
            开始识别
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExerciseRecordingModal;
