import React from 'react';
import Spinner from './Spinner';

interface LoadingScreenProps {
  message?: string;
  fullScreen?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({ 
  message = "正在加载...", 
  fullScreen = true 
}) => {
  const containerClasses = fullScreen 
    ? "fixed inset-0 bg-base-100 z-50 flex items-center justify-center"
    : "flex items-center justify-center p-8";

  return (
    <div className={containerClasses}>
      <div className="text-center">
        <Spinner size="lg" color="primary" className="mx-auto mb-4" />
        <p className="text-base-content/70 text-sm font-medium">{message}</p>
      </div>
    </div>
  );
};

export default LoadingScreen;