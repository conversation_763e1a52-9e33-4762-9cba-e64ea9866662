// 原子级组件导出
export { default as But<PERSON> } from './Button';
export type { ButtonProps } from './Button';

export { default as Input } from './Input';
export type { InputProps } from './Input';

export { default as Card, CardHeader, CardTitle, CardContent, CardFooter } from './Card';
export type { CardProps, CardHeaderProps, CardTitleProps, CardContentProps, CardFooterProps } from './Card';
export {
  default as NumberDisplay,
  WeightDisplay,
  FoodWeightDisplay,
  HeightDisplay,
  AgeDisplay,
  CalorieDisplay,
  BMIDisplay,
  DaysDisplay
} from './NumberDisplay';

export { default as Badge } from './Badge';
export type { BadgeProps } from './Badge';

export { default as Spinner } from './Spinner';
export type { SpinnerProps } from './Spinner';

export { default as LoadingScreen } from './LoadingScreen';