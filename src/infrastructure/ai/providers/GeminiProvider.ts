/**
 * Gemini AI提供商实现
 */

import { AI<PERSON>rovider, AIProviderConfig, AIImageRecognitionResult, AITextAnalysisResult, AIValidationResult } from './AIProvider';
import { promptManager } from '../prompts/promptManager';

export class GeminiProvider implements AIProvider {
  public readonly name = 'gemini';
  
  private apiKey: string = '';
  private baseUrl: string = '';
  private modelName: string = '';
  private timeout: number = 30000;

  /**
   * 初始化提供商
   */
  initialize(config: AIProviderConfig): void {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl;
    this.modelName = config.modelName;
    this.timeout = config.timeout || 30000;
  }

  /**
   * 图片识别
   */
  async recognizeFood(files: File[], additionalContext?: string, _userContext?: any): Promise<AIImageRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      // 将所有图片转换为base64
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      // 使用统一的图片识别方法
      const response = await this.sendImageRequest(base64Images, additionalContext);

      // 解析响应
      return this.parseImageResponse(response);
    } catch (error) {
      console.error('Gemini食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini食物识别失败');
    }
  }

  /**
   * 文本分析
   */
  async analyzeText(text: string): Promise<AITextAnalysisResult> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const response = await this.sendTextRequest(text);
      return this.parseTextResponse(response);
    } catch (error) {
      console.error('Gemini文本分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini文本分析失败');
    }
  }

  /**
   * 营养建议分析
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    if (!this.apiKey) {
      throw new Error('Gemini API密钥未配置');
    }

    try {
      const response = await this.sendAdviceRequest(prompt);
      return this.parseAdviceResponse(response);
    } catch (error) {
      console.error('Gemini营养建议分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'Gemini营养建议分析失败');
    }
  }

  /**
   * 验证配置
   */
  async validateConfig(config: AIProviderConfig): Promise<AIValidationResult> {
    try {
      const response = await fetch(`${config.baseUrl}/v1beta/models`, {
        headers: {
          'x-goog-api-key': config.apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.models || !Array.isArray(result.models)) {
        throw new Error('API响应格式不正确');
      }

      return {
        isValid: true,
        modelList: result.models.map((model: any) => model.name.replace('models/', ''))
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '验证失败'
      };
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(config: AIProviderConfig): Promise<string[]> {
    const response = await fetch(`${config.baseUrl}/v1beta/models`, {
      headers: {
        'x-goog-api-key': config.apiKey,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取Gemini模型列表失败: ${response.status}`);
    }

    const result = await response.json();
    return result.models.map((model: any) => model.name.replace('models/', ''));
  }

  /**
   * 将文件转换为base64
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * 发送图片识别请求
   */
  private async sendImageRequest(base64Images: string[], additionalContext?: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 使用统一的图片识别提示词
      const isMultiImage = base64Images.length > 1;
      const prompt = promptManager.renderPrompt('unified_image_food_recognition', {
        imageCount: base64Images.length,
        multiImage: isMultiImage,
        additionalContext: additionalContext || ''
      });

      // 构建图片parts
      const imageParts = base64Images.map(base64 => ({
        inline_data: {
          mime_type: 'image/jpeg',
          data: base64
        }
      }));

      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt },
                ...imageParts
              ]
            }
          ],
          generationConfig: {
            temperature: 0.1,
            topK: 32,
            topP: 1,
            maxOutputTokens: 8192,
          },
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送文本分析请求
   */
  private async sendTextRequest(text: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const prompt = promptManager.renderPrompt('text_food_analysis', { text });

      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.1,
            topK: 32,
            topP: 1,
            maxOutputTokens: 4096,
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送营养建议请求
   */
  private async sendAdviceRequest(prompt: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}/v1beta/models/${this.modelName}:generateContent`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': this.apiKey,
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                { text: prompt }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 解析图片识别响应
   */
  private parseImageResponse(response: any): AIImageRecognitionResult {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const content = response.candidates[0].content.parts[0].text;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AIImageRecognitionResult;
    } catch (error) {
      console.error('解析图片识别响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析文本分析响应
   */
  private parseTextResponse(response: any): AITextAnalysisResult {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const content = response.candidates[0].content.parts[0].text;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AITextAnalysisResult;
    } catch (error) {
      console.error('解析文本分析响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析营养建议响应
   */
  private parseAdviceResponse(response: any): { advice: string; rawResponse?: any } {
    try {
      if (!response.candidates || !response.candidates[0] || !response.candidates[0].content) {
        throw new Error('API响应格式不正确');
      }

      const advice = response.candidates[0].content.parts[0].text;

      return {
        advice: advice.trim(),
        rawResponse: response
      };
    } catch (error) {
      console.error('解析营养建议响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }
}

// {{ AURA-X: Add - 添加响应解析方法. Approval: 寸止(ID:1736938000). }}
