/**
 * 通用AI提供商接口定义
 */

export interface AIProviderConfig {
  apiKey: string;
  baseUrl: string;
  modelName: string;
  timeout?: number;
}

export interface AIImageRecognitionResult {
  foods: Array<{
    name: string;
    weight: number;
    unit?: 'g' | 'ml' | '个' | '份';
    unitValue?: number;
    calories: number;
    confidence: number;
    dataSource: 'nutrition_label' | 'visual_estimation';
    nutrition: {
      protein: number;
      fat: number;
      carbs: number;
      fiber: number;
      sugar: number;
      sodium: number;
    };
    brand?: string;
    imageIndex?: number;
    labelInfo: {
      hasLabel: boolean;
      confidence: number;
      readableText: string;
    };
    portionAnalysis: {
      estimatedPortion: string;
      referenceObject: string;
      confidenceLevel: 'high' | 'medium' | 'low';
    };
  }>;
  analysisMetadata: {
    hasNutritionLabel: boolean;
    imageQuality: 'high' | 'medium' | 'low';
    recognitionMethod: string;
    processingNotes: string;
  };
  multiImageAnalysis?: {
    totalImages: number;
    duplicatesFound: number;
    crossReferenceNotes: string;
  };
}

export interface AITextAnalysisResult {
  foods: Array<{
    name: string;
    weight: number;
    unit?: 'g' | 'ml' | '个' | '份';
    unitValue?: number;
    calories: number;
    confidence: number;
    dataSource?: 'text_analysis';
    nutrition: {
      protein: number;
      fat: number;
      carbs: number;
      fiber: number;
      sugar: number;
      sodium: number;
    };
    brand?: string;
  }>;
  analysisMetadata: {
    recognitionMethod: string;
    processingNotes: string;
  };
}

// 运动识别结果接口
export interface AIExerciseRecognitionResult {
  exercises: Array<{
    name: string;
    type: 'cardio' | 'strength' | 'flexibility' | 'sports' | 'daily';
    duration: number; // 分钟
    intensity: 'low' | 'medium' | 'high';
    caloriesBurned: number;
    confidence: number;
    dataSource: 'image_recognition' | 'text_analysis';
    equipment?: string[];
    muscleGroups?: string[];
    imageIndex?: number;
  }>;
  analysisMetadata: {
    recognitionMethod: string;
    processingNotes: string;
    totalDuration: number;
    totalCaloriesBurned: number;
    intensityLevel: 'low' | 'medium' | 'high';
  };
}

export interface AIValidationResult {
  isValid: boolean;
  error?: string;
  modelList?: string[];
}

/**
 * 通用AI提供商接口
 */
export interface AIProvider {
  /**
   * 提供商名称
   */
  readonly name: string;

  /**
   * 初始化提供商
   */
  initialize(config: AIProviderConfig): void;

  /**
   * 图片识别
   */
  recognizeFood(files: File[], additionalContext?: string, userContext?: any): Promise<AIImageRecognitionResult>;

  /**
   * 文本分析
   */
  analyzeText(text: string, additionalContext?: string): Promise<AITextAnalysisResult>;

  /**
   * 运动文字分析
   */
  analyzeExerciseText(text: string, additionalContext?: string, userContext?: any): Promise<AIExerciseRecognitionResult>;

  /**
   * 运动图像识别
   */
  recognizeExercise(files: File[], additionalContext?: string, userContext?: any): Promise<AIExerciseRecognitionResult>;

  /**
   * 营养建议分析
   */
  analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }>;

  /**
   * 生成营养建议
   */
  generateNutritionAdvice(foods: any[], userContext?: any): Promise<string>;

  /**
   * 生成运动建议
   */
  generateExerciseRecommendations(exercises: any[], userContext?: any): Promise<string>;

  /**
   * 验证配置
   */
  validateConfig(config: AIProviderConfig): Promise<AIValidationResult>;

  /**
   * 获取可用模型列表
   */
  getAvailableModels(config: AIProviderConfig): Promise<string[]>;
}

// {{ AURA-X: Add - 创建通用AI提供商接口. Approval: 寸止(ID:**********). }}
