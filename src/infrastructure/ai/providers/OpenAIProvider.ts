/**
 * OpenAI AI提供商实现
 */

import { AIProvider, AIProviderConfig, AIImageRecognitionResult, AITextAnalysisResult, AIValidationResult } from './AIProvider';
import { promptManager } from '../prompts/promptManager';

export class OpenAIProvider implements AIProvider {
  public readonly name = 'openai';
  
  private apiKey: string = '';
  private baseUrl: string = '';
  private modelName: string = '';
  private timeout: number = 30000;

  /**
   * 初始化提供商
   */
  initialize(config: AIProviderConfig): void {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl;
    this.modelName = config.modelName;
    this.timeout = config.timeout || 30000;
  }

  /**
   * 图片识别
   */
  async recognizeFood(files: File[], additionalContext?: string, _userContext?: any): Promise<AIImageRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    try {
      // 将所有图片转换为base64
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      // 使用统一的图片识别方法
      const response = await this.sendImageRequest(base64Images, additionalContext);

      // 解析响应
      return this.parseImageResponse(response);
    } catch (error) {
      console.error('OpenAI食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : 'OpenAI食物识别失败');
    }
  }

  /**
   * 文本分析
   */
  async analyzeText(text: string): Promise<AITextAnalysisResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    try {
      const response = await this.sendTextRequest(text);
      return this.parseTextResponse(response);
    } catch (error) {
      console.error('OpenAI文本分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'OpenAI文本分析失败');
    }
  }

  /**
   * 营养建议分析
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    if (!this.apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    try {
      const response = await this.sendAdviceRequest(prompt);
      return this.parseAdviceResponse(response);
    } catch (error) {
      console.error('OpenAI营养建议分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'OpenAI营养建议分析失败');
    }
  }

  /**
   * 验证配置
   */
  async validateConfig(config: AIProviderConfig): Promise<AIValidationResult> {
    try {
      const response = await fetch(`${config.baseUrl}/v1/models`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.data || !Array.isArray(result.data)) {
        throw new Error('API响应格式不正确');
      }

      return {
        isValid: true,
        modelList: result.data.map((model: any) => model.id)
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '验证失败'
      };
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(config: AIProviderConfig): Promise<string[]> {
    const response = await fetch(`${config.baseUrl}/v1/models`, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取OpenAI模型列表失败: ${response.status}`);
    }

    const result = await response.json();
    return result.data.map((model: any) => model.id);
  }

  /**
   * 将文件转换为base64
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * 发送图片识别请求
   */
  private async sendImageRequest(base64Images: string[], additionalContext?: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // 使用统一的图片识别提示词
      const isMultiImage = base64Images.length > 1;
      const prompt = promptManager.renderPrompt('unified_image_food_recognition', {
        imageCount: base64Images.length,
        multiImage: isMultiImage,
        additionalContext: additionalContext || ''
      });

      // 构建消息内容
      const content: any[] = [
        { type: 'text', text: prompt }
      ];

      // 添加图片
      base64Images.forEach(base64 => {
        content.push({
          type: 'image_url',
          image_url: {
            url: base64,
            detail: 'high'
          }
        });
      });

      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: content
            }
          ],
          max_tokens: 4096,
          temperature: 0.1
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送文本分析请求
   */
  private async sendTextRequest(text: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const prompt = promptManager.renderPrompt('text_food_analysis', { text });

      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 2048,
          temperature: 0.1
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送营养建议请求
   */
  private async sendAdviceRequest(prompt: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 1024,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 解析图片识别响应
   */
  private parseImageResponse(response: any): AIImageRecognitionResult {
    try {
      if (!response.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('API响应格式不正确');
      }

      const content = response.choices[0].message.content;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AIImageRecognitionResult;
    } catch (error) {
      console.error('解析OpenAI图片识别响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析文本分析响应
   */
  private parseTextResponse(response: any): AITextAnalysisResult {
    try {
      if (!response.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('API响应格式不正确');
      }

      const content = response.choices[0].message.content;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AITextAnalysisResult;
    } catch (error) {
      console.error('解析OpenAI文本分析响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析营养建议响应
   */
  private parseAdviceResponse(response: any): { advice: string; rawResponse?: any } {
    try {
      if (!response.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('API响应格式不正确');
      }

      const advice = response.choices[0].message.content;

      return {
        advice: advice.trim(),
        rawResponse: response
      };
    } catch (error) {
      console.error('解析OpenAI营养建议响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }
}

// {{ AURA-X: Add - 添加OpenAI响应解析方法. Approval: 寸止(ID:1736938100). }}
