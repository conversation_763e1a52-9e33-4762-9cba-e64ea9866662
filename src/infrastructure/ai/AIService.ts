/**
 * 统一AI服务管理器
 * 根据用户选择的AI模型自动使用对应的提供商
 */

import { aiProviderManager } from './providers/AIProviderManager';
import { AIImageRecognitionResult, AITextAnalysisResult } from './providers/AIProvider';
import { useAIModelStore } from '../../domains/ai/stores/aiModelStore';

export class AIService {
  private static instance: AIService;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * 初始化当前活跃的AI提供商
   */
  private initializeCurrentProvider(): void {
    const { getActiveModel } = useAIModelStore.getState();
    const activeModel = getActiveModel();

    if (!activeModel) {
      throw new Error('未配置活跃的AI模型，请先在设置中配置AI模型');
    }

    // 设置当前提供商
    aiProviderManager.setProvider(activeModel.provider, {
      apiKey: activeModel.apiKey,
      baseUrl: activeModel.baseUrl,
      modelName: activeModel.modelName
    });
  }

  /**
   * 图片食物识别
   */
  async recognizeFood(files: File[], additionalContext?: string, userContext?: any): Promise<AIImageRecognitionResult> {
    this.initializeCurrentProvider();
    
    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.recognizeFood(files, additionalContext, userContext);
  }

  /**
   * 文本食物分析
   */
  async analyzeText(text: string, additionalContext?: string): Promise<AITextAnalysisResult> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.analyzeText(text, additionalContext);
  }

  /**
   * 运动文字分析
   */
  async analyzeExerciseText(text: string, additionalContext?: string, userContext?: any): Promise<any> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.analyzeExerciseText(text, additionalContext, userContext);
  }

  /**
   * 运动图像识别
   */
  async recognizeExercise(files: File[], additionalContext?: string, userContext?: any): Promise<any> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.recognizeExercise(files, additionalContext, userContext);
  }

  /**
   * 生成营养建议
   */
  async generateNutritionAdvice(foods: any[], userContext?: any): Promise<string> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.generateNutritionAdvice(foods, userContext);
  }

  /**
   * 生成运动建议
   */
  async generateExerciseRecommendations(exercises: any[], userContext?: any): Promise<string> {
    this.initializeCurrentProvider();

    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.generateExerciseRecommendations(exercises, userContext);
  }

  /**
   * 营养建议分析
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    this.initializeCurrentProvider();
    
    const provider = aiProviderManager.getCurrentProvider();
    if (!provider) {
      throw new Error('AI提供商未初始化');
    }

    return await provider.analyzeNutritionAdvice(prompt);
  }

  /**
   * 获取当前使用的AI模型信息
   */
  getCurrentModelInfo(): { provider: string; modelName: string } | null {
    const { getActiveModel } = useAIModelStore.getState();
    const activeModel = getActiveModel();

    if (!activeModel) {
      return null;
    }

    return {
      provider: activeModel.provider,
      modelName: activeModel.modelName
    };
  }

  /**
   * 检查是否有可用的AI模型
   */
  hasActiveModel(): boolean {
    const { getActiveModel } = useAIModelStore.getState();
    return getActiveModel() !== null;
  }
}

// 创建全局实例
export const aiService = AIService.getInstance();

// {{ AURA-X: Add - 创建统一AI服务管理器. Approval: 寸止(ID:**********). }}
